import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["customQuantityField"]

  connect() {
    // On load, check if "custom" radio is selected
    const customRadio = this.element.querySelector('input[type="radio"][value="custom"]:checked')
    if (customRadio) {
      this.showCustomQuantityField()
    } else {
      this.hideCustomQuantityField()
    }
  }

  toggle(event) {
    if (event.target.value === "custom") {
      this.showCustomQuantityField()
    } else {
      this.hideCustomQuantityField()
    }
  }

  showCustomQuantityField() {
    this.customQuantityFieldTarget.style.display = "block"
  }

  hideCustomQuantityField() {
    this.customQuantityFieldTarget.style.display = "none"
  }
}
