# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_06_25_094135) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "action_mailbox_inbound_emails", force: :cascade do |t|
    t.integer "status", default: 0, null: false
    t.string "message_id", null: false
    t.string "message_checksum", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["message_id", "message_checksum"], name: "index_action_mailbox_inbound_emails_uniqueness", unique: true
  end

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum", null: false
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "friendly_id_slugs", force: :cascade do |t|
    t.string "slug", null: false
    t.bigint "sluggable_id", null: false
    t.string "sluggable_type", limit: 50
    t.string "scope"
    t.datetime "created_at"
    t.datetime "deleted_at"
    t.string "locale"
    t.index ["deleted_at"], name: "index_friendly_id_slugs_on_deleted_at"
    t.index ["locale"], name: "index_friendly_id_slugs_on_locale"
    t.index ["slug", "sluggable_type", "locale"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type_and_locale"
    t.index ["slug", "sluggable_type", "scope", "locale"], name: "index_friendly_id_slugs_unique", unique: true
    t.index ["sluggable_id"], name: "index_friendly_id_slugs_on_sluggable_id"
    t.index ["sluggable_type"], name: "index_friendly_id_slugs_on_sluggable_type"
  end

  create_table "remarks", force: :cascade do |t|
    t.string "remarkable_type", null: false
    t.bigint "remarkable_id", null: false
    t.string "content", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["remarkable_type", "remarkable_id"], name: "index_remarks_on_remarkable"
  end

  create_table "shortened_urls", id: :serial, force: :cascade do |t|
    t.integer "owner_id"
    t.string "owner_type", limit: 20
    t.text "url", null: false
    t.string "unique_key", limit: 10, null: false
    t.string "category"
    t.integer "use_count", default: 0, null: false
    t.datetime "expires_at"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["category"], name: "index_shortened_urls_on_category"
    t.index ["owner_id", "owner_type"], name: "index_shortened_urls_on_owner_id_and_owner_type"
    t.index ["unique_key"], name: "index_shortened_urls_on_unique_key", unique: true
    t.index ["url"], name: "index_shortened_urls_on_url"
  end

  create_table "spree_action_logs", force: :cascade do |t|
    t.string "action_name"
    t.string "action_place"
    t.string "role"
    t.string "email"
    t.datetime "date"
    t.bigint "user_id"
    t.bigint "product_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_spree_action_logs_on_product_id"
    t.index ["user_id"], name: "index_spree_action_logs_on_user_id"
  end

  create_table "spree_active_shipping_settings", force: :cascade do |t|
    t.jsonb "fedex", default: {"fedex_key"=>"", "fedex_login"=>"", "fedex_account"=>"", "fedex_password"=>""}
    t.jsonb "usps", default: {"usps_login"=>"", "usps_commercial_base"=>"", "usps_commercial_plus"=>""}
    t.jsonb "ups", default: {"ups_key"=>"", "ups_login"=>"", "ups_password"=>"", "shipper_number"=>""}
    t.jsonb "canada_post", default: {"canada_post_login"=>""}
    t.jsonb "australia_post", default: {"australia_post_login"=>""}
    t.jsonb "general_settings", default: {"units"=>"", "test_mode"=>"", "handling_fee"=>"", "default_weight"=>"", "unit_multiplier"=>"", "max_weight_per_package"=>""}
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["store_id"], name: "index_spree_active_shipping_settings_on_store_id"
  end

  create_table "spree_activity_logs", force: :cascade do |t|
    t.bigint "user_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "date"
    t.string "action"
    t.string "action_place"
    t.string "role"
    t.string "name"
    t.string "email"
    t.string "action_name"
    t.string "loggable_type"
    t.bigint "loggable_id"
    t.bigint "product_id"
    t.index ["loggable_type", "loggable_id"], name: "index_spree_activity_logs_on_loggable"
    t.index ["user_id"], name: "index_spree_activity_logs_on_user_id"
  end

  create_table "spree_addresses", force: :cascade do |t|
    t.string "firstname"
    t.string "lastname"
    t.string "address1"
    t.string "address2"
    t.string "city"
    t.string "zipcode"
    t.string "phone"
    t.string "state_name"
    t.string "alternative_phone"
    t.string "company"
    t.bigint "state_id"
    t.bigint "country_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "user_id"
    t.datetime "deleted_at"
    t.string "label"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.index ["country_id"], name: "index_spree_addresses_on_country_id"
    t.index ["deleted_at"], name: "index_spree_addresses_on_deleted_at"
    t.index ["firstname"], name: "index_addresses_on_firstname"
    t.index ["lastname"], name: "index_addresses_on_lastname"
    t.index ["state_id"], name: "index_spree_addresses_on_state_id"
    t.index ["user_id"], name: "index_spree_addresses_on_user_id"
  end

  create_table "spree_adjustments", force: :cascade do |t|
    t.string "source_type"
    t.bigint "source_id"
    t.string "adjustable_type"
    t.bigint "adjustable_id"
    t.decimal "amount", precision: 10, scale: 2
    t.string "label"
    t.boolean "mandatory"
    t.boolean "eligible", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "state"
    t.bigint "order_id", null: false
    t.boolean "included", default: false
    t.index ["adjustable_id", "adjustable_type"], name: "index_spree_adjustments_on_adjustable_id_and_adjustable_type"
    t.index ["eligible"], name: "index_spree_adjustments_on_eligible"
    t.index ["order_id"], name: "index_spree_adjustments_on_order_id"
    t.index ["source_id", "source_type"], name: "index_spree_adjustments_on_source_id_and_source_type"
  end

  create_table "spree_admin_users", force: :cascade do |t|
    t.string "email"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "encrypted_otp_secret"
    t.string "encrypted_otp_secret_iv"
    t.string "encrypted_otp_secret_salt"
    t.integer "consumed_timestep"
    t.boolean "otp_required_for_login", default: false
    t.boolean "two_factor_enabled", default: false
  end

  create_table "spree_amazon_lwa_credentials", force: :cascade do |t|
    t.string "client_id"
    t.string "client_secret"
    t.datetime "expiry_date"
    t.boolean "refreshed", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "spree_amazon_report_details", force: :cascade do |t|
    t.string "report_id"
    t.string "tenant_name"
    t.integer "status"
    t.string "store_id"
    t.string "oauth_application_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "spree_amazon_shipment_details", force: :cascade do |t|
    t.bigint "order_package_id"
    t.string "amazon_shipment_request_token"
    t.string "amazon_shipment_rate_id"
    t.string "amazon_shipment_carrier_id"
    t.string "amazon_shipment_id"
    t.string "amazon_shipment_tracking_id"
    t.string "amazon_shipment_reverse_tracking_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["order_package_id"], name: "index_spree_amazon_shipment_details_on_order_package_id"
  end

  create_table "spree_announcements", force: :cascade do |t|
    t.string "subject_type"
    t.bigint "subject_id"
    t.bigint "store_id"
    t.string "title"
    t.string "subject_url"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["store_id"], name: "index_spree_announcements_on_store_id"
    t.index ["subject_type", "subject_id"], name: "index_spree_announcements_on_subject"
  end

  create_table "spree_assets", force: :cascade do |t|
    t.string "viewable_type"
    t.bigint "viewable_id"
    t.integer "attachment_width"
    t.integer "attachment_height"
    t.integer "attachment_file_size"
    t.integer "position"
    t.string "attachment_content_type"
    t.string "attachment_file_name"
    t.string "type", limit: 75
    t.datetime "attachment_updated_at"
    t.text "alt"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.string "title"
    t.string "display_on"
    t.integer "category"
    t.integer "sort_number"
    t.bigint "store_id"
    t.string "subtitle"
    t.string "overlay_type"
    t.string "overlay_text"
    t.string "overlay_position"
    t.string "overlay_color"
    t.string "overlay_link"
    t.string "alignment_position", default: "Center", null: false
    t.string "link"
    t.index ["position"], name: "index_spree_assets_on_position"
    t.index ["store_id"], name: "index_spree_assets_on_store_id"
    t.index ["viewable_id"], name: "index_assets_on_viewable_id"
    t.index ["viewable_type", "type"], name: "index_assets_on_viewable_type_and_type"
  end

  create_table "spree_blog_categories", force: :cascade do |t|
    t.string "name"
    t.string "slug"
    t.bigint "store_id"
    t.boolean "visible", default: true
    t.datetime "deleted_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "position"
  end

  create_table "spree_blog_likes", force: :cascade do |t|
    t.integer "user_id"
    t.integer "blog_post_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["user_id", "blog_post_id"], name: "index_spree_blog_likes_on_user_id_and_blog_post_id", unique: true
  end

  create_table "spree_blog_post_attachments", force: :cascade do |t|
    t.string "title"
    t.string "alt"
    t.bigint "blog_post_id"
    t.bigint "user_id"
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "spree_blog_posts", force: :cascade do |t|
    t.string "title"
    t.boolean "visible", default: true
    t.bigint "blog_category_id"
    t.text "description"
    t.string "slug"
    t.datetime "deleted_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "published_at"
    t.text "summary"
    t.integer "position"
    t.integer "views_count", default: 0
    t.integer "likes_count", default: 0
    t.string "meta_title"
    t.text "meta_description"
    t.string "meta_keywords"
    t.string "author_name"
  end

  create_table "spree_calculators", force: :cascade do |t|
    t.string "type"
    t.string "calculable_type"
    t.bigint "calculable_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.text "preferences"
    t.datetime "deleted_at"
    t.index ["calculable_id", "calculable_type"], name: "index_spree_calculators_on_calculable_id_and_calculable_type"
    t.index ["deleted_at"], name: "index_spree_calculators_on_deleted_at"
    t.index ["id", "type"], name: "index_spree_calculators_on_id_and_type"
  end

  create_table "spree_campaign_subscribers", force: :cascade do |t|
    t.integer "campaign_id"
    t.integer "subscriber_id"
    t.boolean "sent", default: false
    t.boolean "delivered", default: false
    t.boolean "opened", default: false
    t.boolean "clicked", default: false
    t.boolean "unsubscribed", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "message_id"
    t.text "error_message"
    t.boolean "failed", default: false
  end

  create_table "spree_campaigns", force: :cascade do |t|
    t.string "title"
    t.datetime "scheduled_date"
    t.integer "status", default: 0
    t.integer "template_id"
    t.string "sender_email"
    t.integer "sent_count", default: 0
    t.integer "delivered_count", default: 0
    t.integer "opened_count", default: 0
    t.integer "clicked_count", default: 0
    t.integer "unsubscribed_count", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "store_id"
    t.integer "failed_count", default: 0
  end

  create_table "spree_checks", force: :cascade do |t|
    t.bigint "payment_method_id"
    t.bigint "user_id"
    t.string "account_holder_name"
    t.string "account_holder_type"
    t.string "routing_number"
    t.string "account_number"
    t.string "account_type", default: "checking"
    t.string "status"
    t.string "last_digits"
    t.string "gateway_customer_profile_id"
    t.string "gateway_payment_profile_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "deleted_at"
    t.index ["payment_method_id"], name: "index_spree_checks_on_payment_method_id"
    t.index ["user_id"], name: "index_spree_checks_on_user_id"
  end

  create_table "spree_cms_pages", force: :cascade do |t|
    t.string "title", null: false
    t.string "meta_title"
    t.text "content"
    t.text "meta_description"
    t.boolean "visible", default: true
    t.string "slug"
    t.string "type"
    t.string "locale"
    t.datetime "deleted_at"
    t.bigint "store_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "settings", default: {}
    t.index ["deleted_at"], name: "index_spree_cms_pages_on_deleted_at"
    t.index ["slug", "store_id", "deleted_at"], name: "index_spree_cms_pages_on_slug_and_store_id_and_deleted_at", unique: true
    t.index ["store_id", "locale", "type"], name: "index_spree_cms_pages_on_store_id_and_locale_and_type"
    t.index ["store_id"], name: "index_spree_cms_pages_on_store_id"
    t.index ["title", "type", "store_id"], name: "index_spree_cms_pages_on_title_and_type_and_store_id"
    t.index ["visible"], name: "index_spree_cms_pages_on_visible"
  end

  create_table "spree_cms_sections", force: :cascade do |t|
    t.string "name", null: false
    t.text "content"
    t.text "settings"
    t.string "fit"
    t.string "destination"
    t.string "type"
    t.integer "position"
    t.string "linked_resource_type"
    t.bigint "linked_resource_id"
    t.bigint "cms_page_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "listing_id"
    t.boolean "visible", default: true, null: false
    t.jsonb "properties", default: {}, null: false
    t.index ["cms_page_id"], name: "index_spree_cms_sections_on_cms_page_id"
    t.index ["linked_resource_type", "linked_resource_id"], name: "index_spree_cms_sections_on_linked_resource"
    t.index ["position"], name: "index_spree_cms_sections_on_position"
    t.index ["type"], name: "index_spree_cms_sections_on_type"
  end

  create_table "spree_conditions", force: :cascade do |t|
    t.string "name"
    t.string "condition_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "store_id"
    t.bigint "sale_channel_id"
    t.index ["sale_channel_id"], name: "index_spree_conditions_on_sale_channel_id"
    t.index ["store_id"], name: "index_spree_conditions_on_store_id"
  end

  create_table "spree_countries", force: :cascade do |t|
    t.string "iso_name"
    t.string "iso", null: false
    t.string "iso3", null: false
    t.string "name"
    t.integer "numcode"
    t.boolean "states_required", default: false
    t.datetime "updated_at"
    t.boolean "zipcode_required", default: true
    t.datetime "created_at"
    t.index ["iso"], name: "index_spree_countries_on_iso", unique: true
    t.index ["iso3"], name: "index_spree_countries_on_iso3", unique: true
    t.index ["iso_name"], name: "index_spree_countries_on_iso_name", unique: true
    t.index ["name"], name: "index_spree_countries_on_name", unique: true
  end

  create_table "spree_credit_cards", force: :cascade do |t|
    t.string "month"
    t.string "year"
    t.string "cc_type"
    t.string "last_digits"
    t.bigint "address_id"
    t.string "gateway_customer_profile_id"
    t.string "gateway_payment_profile_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "name"
    t.bigint "user_id"
    t.bigint "payment_method_id"
    t.boolean "default", default: false, null: false
    t.datetime "deleted_at"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.index ["address_id"], name: "index_spree_credit_cards_on_address_id"
    t.index ["deleted_at"], name: "index_spree_credit_cards_on_deleted_at"
    t.index ["payment_method_id"], name: "index_spree_credit_cards_on_payment_method_id"
    t.index ["user_id"], name: "index_spree_credit_cards_on_user_id"
  end

  create_table "spree_customer_returns", force: :cascade do |t|
    t.string "number"
    t.bigint "stock_location_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "store_id"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.index ["number"], name: "index_spree_customer_returns_on_number", unique: true
    t.index ["stock_location_id"], name: "index_spree_customer_returns_on_stock_location_id"
    t.index ["store_id"], name: "index_spree_customer_returns_on_store_id"
  end

  create_table "spree_customer_shipments", force: :cascade do |t|
    t.bigint "return_authorization_id"
    t.string "number"
    t.decimal "weight", precision: 8, scale: 2
    t.string "easypost_shipment_id"
    t.string "tracking"
    t.text "tracking_label"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["number"], name: "index_spree_customer_shipments_on_number"
    t.index ["return_authorization_id"], name: "index_spree_customer_shipments_on_return_authorization_id"
    t.index ["tracking"], name: "index_spree_customer_shipments_on_tracking"
  end

  create_table "spree_daily_sales_records", force: :cascade do |t|
    t.bigint "product_id"
    t.date "date"
    t.integer "quantity", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["date"], name: "index_spree_daily_sales_records_on_date"
    t.index ["product_id", "date"], name: "index_spree_daily_sales_records_on_product_id_and_date", unique: true
  end

  create_table "spree_data_feeds", force: :cascade do |t|
    t.bigint "store_id"
    t.string "name"
    t.string "type"
    t.string "slug"
    t.boolean "active", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["store_id", "slug", "type"], name: "index_spree_data_feeds_on_store_id_and_slug_and_type"
    t.index ["store_id"], name: "index_spree_data_feeds_on_store_id"
  end

  create_table "spree_digital_links", force: :cascade do |t|
    t.bigint "digital_id"
    t.bigint "line_item_id"
    t.string "token"
    t.integer "access_counter"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["digital_id"], name: "index_spree_digital_links_on_digital_id"
    t.index ["line_item_id"], name: "index_spree_digital_links_on_line_item_id"
    t.index ["token"], name: "index_spree_digital_links_on_token", unique: true
  end

  create_table "spree_digitals", force: :cascade do |t|
    t.bigint "variant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["variant_id"], name: "index_spree_digitals_on_variant_id"
  end

  create_table "spree_easypost_settings", force: :cascade do |t|
    t.string "customs_signer"
    t.string "customs_contents_type"
    t.string "customs_eel_pfc"
    t.string "carrier_accounts_shipping"
    t.string "carrier_accounts_returns"
    t.string "endorsement_type"
    t.integer "returns_stock_location_id"
    t.boolean "buy_postage_when_shipped", default: false
    t.boolean "use_easypost_on_frontend", default: false
    t.boolean "validate_address", default: false
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "key"
    t.string "name"
    t.index ["store_id"], name: "index_spree_easypost_settings_on_store_id"
  end

  create_table "spree_ebay_policies", force: :cascade do |t|
    t.string "policy_type"
    t.jsonb "response_hash"
    t.string "policy_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "store_id"
    t.bigint "sale_channel_id"
    t.index ["sale_channel_id"], name: "index_spree_ebay_policies_on_sale_channel_id"
    t.index ["store_id"], name: "index_spree_ebay_policies_on_store_id"
  end

  create_table "spree_elasticsearch_sync_records", force: :cascade do |t|
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.integer "op", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["record_id"], name: "index_spree_elasticsearch_sync_records_on_record_id"
    t.index ["record_type"], name: "index_spree_elasticsearch_sync_records_on_record_type"
  end

  create_table "spree_email_confirmations", force: :cascade do |t|
    t.string "email"
    t.string "otp_code"
    t.boolean "confirmed", default: false
    t.datetime "expires_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "spree_email_settings", force: :cascade do |t|
    t.string "email_from"
    t.string "email_bcc"
    t.string "intercept_email"
    t.json "smtp", default: {"domain"=>"", "address"=>"", "port"=>"", "secure_connection_type"=>"", "authentication"=>"", "user_name"=>"", "password"=>""}
    t.boolean "mail_delivery"
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "email_setting_type", default: 0
    t.string "api_key"
    t.index ["store_id"], name: "index_spree_email_settings_on_store_id"
  end

  create_table "spree_email_template_tags", id: false, force: :cascade do |t|
    t.bigint "tag_id"
    t.bigint "email_template_id"
    t.index ["email_template_id"], name: "index_spree_email_template_tags_on_email_template_id"
    t.index ["tag_id"], name: "index_spree_email_template_tags_on_tag_id"
  end

  create_table "spree_email_templates", force: :cascade do |t|
    t.string "template_name"
    t.boolean "active"
    t.string "subject"
    t.string "body"
    t.string "variables", array: true
    t.string "description"
    t.string "mailer_class"
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "template_type", default: "other"
    t.index ["store_id"], name: "index_spree_email_templates_on_store_id"
  end

  create_table "spree_featured_products", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "position"
    t.integer "listing_id"
    t.index ["store_id"], name: "index_spree_featured_products_on_store_id"
  end

  create_table "spree_frequently_boughts", force: :cascade do |t|
    t.bigint "listing_id"
    t.bigint "matched_listing_id"
    t.integer "position"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["listing_id"], name: "index_spree_frequently_boughts_on_listing_id"
  end

  create_table "spree_gallery_items", force: :cascade do |t|
    t.string "title"
    t.string "uploaded_by"
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["store_id"], name: "index_spree_gallery_items_on_store_id"
  end

  create_table "spree_gateways", force: :cascade do |t|
    t.string "type"
    t.string "name"
    t.text "description"
    t.boolean "active", default: true
    t.string "environment", default: "development"
    t.string "server", default: "test"
    t.boolean "test_mode", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.text "preferences"
    t.index ["active"], name: "index_spree_gateways_on_active"
    t.index ["test_mode"], name: "index_spree_gateways_on_test_mode"
  end

  create_table "spree_historical_orders", force: :cascade do |t|
    t.string "number", limit: 32, null: false
    t.string "state"
    t.string "shipment_state"
    t.string "payment_state"
    t.decimal "total", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "item_total", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "payment_total", precision: 10, scale: 2
    t.string "currency", default: "USD"
    t.bigint "store_id"
    t.bigint "user_id"
    t.string "email"
    t.string "channel"
    t.jsonb "sale_channel_metadata", default: {}
    t.jsonb "line_items", default: {}
    t.jsonb "shipment_details", default: {}
    t.jsonb "payment_history", default: {}
    t.jsonb "fees", default: {}
    t.jsonb "buyer_info", default: {}
    t.datetime "order_date", null: false
    t.datetime "last_modified_date"
    t.jsonb "additional_data"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["line_items"], name: "index_spree_historical_orders_on_line_items", using: :gin
    t.index ["number"], name: "index_spree_historical_orders_on_number"
    t.index ["payment_history"], name: "index_spree_historical_orders_on_payment_history", using: :gin
    t.index ["sale_channel_metadata"], name: "index_spree_historical_orders_on_sale_channel_metadata", using: :gin
    t.index ["shipment_details"], name: "index_spree_historical_orders_on_shipment_details", using: :gin
  end

  create_table "spree_image_uploads", force: :cascade do |t|
    t.string "imageable_type", null: false
    t.bigint "imageable_id", null: false
    t.bigint "user_id", null: false
    t.integer "image_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "position", default: 0
    t.index ["imageable_type", "imageable_id"], name: "index_spree_image_uploads_on_imageable"
    t.index ["user_id"], name: "index_spree_image_uploads_on_user_id"
  end

  create_table "spree_import_logs", force: :cascade do |t|
    t.string "started_by_email"
    t.integer "store_id"
    t.string "state"
    t.integer "success_row_count"
    t.integer "error_row_count"
    t.json "error_details", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "initiated_for"
    t.integer "log_type"
  end

  create_table "spree_inventory_units", force: :cascade do |t|
    t.string "state"
    t.bigint "variant_id"
    t.bigint "order_id"
    t.bigint "shipment_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "pending", default: true
    t.bigint "line_item_id"
    t.integer "quantity", default: 1
    t.bigint "original_return_item_id"
    t.index ["line_item_id"], name: "index_spree_inventory_units_on_line_item_id"
    t.index ["order_id"], name: "index_inventory_units_on_order_id"
    t.index ["original_return_item_id"], name: "index_spree_inventory_units_on_original_return_item_id"
    t.index ["shipment_id"], name: "index_inventory_units_on_shipment_id"
    t.index ["variant_id"], name: "index_inventory_units_on_variant_id"
  end

  create_table "spree_invoice_settings", force: :cascade do |t|
    t.string "name"
    t.string "address"
    t.string "city"
    t.string "zipcode"
    t.string "state_name"
    t.string "state_id"
    t.bigint "country_id"
    t.string "message"
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["store_id"], name: "index_spree_invoice_settings_on_store_id"
  end

  create_table "spree_line_items", force: :cascade do |t|
    t.bigint "variant_id"
    t.bigint "order_id"
    t.integer "quantity", null: false
    t.decimal "price", precision: 10, scale: 2, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "currency"
    t.decimal "cost_price", precision: 10, scale: 2
    t.bigint "tax_category_id"
    t.decimal "adjustment_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "additional_tax_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "promo_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "included_tax_total", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "pre_tax_amount", precision: 12, scale: 4, default: "0.0", null: false
    t.decimal "taxable_adjustment_total", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "non_taxable_adjustment_total", precision: 10, scale: 2, default: "0.0", null: false
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.string "ebay_item_id"
    t.integer "pack_size", default: 1
    t.string "ebay_line_item_id"
    t.boolean "subscription_enabled", default: false, null: false
    t.string "subscription_interval"
    t.integer "subscription_quantity"
    t.string "amazon_line_item_id"
    t.bigint "listing_id"
    t.string "walmart_line_item_id"
    t.boolean "is_recurring", default: false
    t.index ["listing_id"], name: "index_spree_line_items_on_listing_id"
    t.index ["order_id"], name: "index_spree_line_items_on_order_id"
    t.index ["tax_category_id"], name: "index_spree_line_items_on_tax_category_id"
    t.index ["variant_id"], name: "index_spree_line_items_on_variant_id"
  end

  create_table "spree_line_items_ext", id: :bigint, default: nil, force: :cascade do |t|
    t.jsonb "additional_data", default: {}, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["additional_data"], name: "index_spree_line_items_ext_on_additional_data", using: :gin
  end

  create_table "spree_listing_collections", force: :cascade do |t|
    t.bigint "store_id"
    t.string "name", null: false
    t.integer "listing_ids", array: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "slug"
    t.text "description"
    t.index ["store_id"], name: "index_spree_listing_collections_on_store_id"
  end

  create_table "spree_listing_inventories", force: :cascade do |t|
    t.integer "total_listed_qty", default: 0
    t.integer "total_sold_qty", default: 0
    t.integer "total_available_qty", default: 0
    t.integer "variant_id"
    t.integer "listing_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "low_availability_toggle", default: false
    t.integer "low_availability_value", default: 0
    t.float "price"
    t.decimal "compare_at_price", precision: 10, scale: 2
    t.decimal "compare_to_price", precision: 10, scale: 2
    t.boolean "is_selected", default: false
  end

  create_table "spree_listings", force: :cascade do |t|
    t.string "title"
    t.string "sku"
    t.date "start_time"
    t.date "end_time"
    t.string "category_id"
    t.string "item_id"
    t.string "quantity"
    t.string "currency"
    t.text "description"
    t.bigint "product_id"
    t.string "item_url"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "status"
    t.bigint "store_id"
    t.string "stock_item_id"
    t.jsonb "ebay_item_specifics"
    t.jsonb "variant_stock_items_data"
    t.string "category_name"
    t.string "shipping_id"
    t.string "payment_id"
    t.string "return_id"
    t.string "condition_id"
    t.bigint "sale_channel_id"
    t.boolean "pack_size_toggle", default: false
    t.integer "pack_size_value", default: 1
    t.jsonb "image_classifier", default: {}
    t.jsonb "auction_pricing"
    t.string "uploaded_by"
    t.json "sale_channel_hash", default: {}
    t.boolean "allow_offer", default: false
    t.decimal "minimum_offer_price", precision: 10, scale: 2
    t.decimal "autoaccept_offer_price", precision: 10, scale: 2
    t.integer "offer_made_count", default: 0
    t.jsonb "subscription_details", default: {}
    t.jsonb "sale_channel_metadata"
    t.bigint "shipping_category_id"
    t.index ["product_id"], name: "index_spree_listings_on_product_id"
    t.index ["sale_channel_id"], name: "index_spree_listings_on_sale_channel_id"
    t.index ["store_id"], name: "index_spree_listings_on_store_id"
  end

  create_table "spree_log_entries", force: :cascade do |t|
    t.string "source_type"
    t.bigint "source_id"
    t.text "details"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["source_id", "source_type"], name: "index_spree_log_entries_on_source_id_and_source_type"
  end

  create_table "spree_match_its", force: :cascade do |t|
    t.bigint "matched_product_id"
    t.bigint "product_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "position"
    t.bigint "listing_id"
    t.index ["product_id"], name: "index_spree_match_its_on_product_id"
  end

  create_table "spree_menu_items", force: :cascade do |t|
    t.string "name", null: false
    t.string "subtitle"
    t.string "destination"
    t.boolean "new_window", default: false
    t.string "item_type"
    t.string "linked_resource_type", default: "Spree::Linkable::Uri"
    t.bigint "linked_resource_id"
    t.string "code"
    t.bigint "parent_id"
    t.bigint "lft", null: false
    t.bigint "rgt", null: false
    t.integer "depth", default: 0, null: false
    t.bigint "menu_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "listing_id"
    t.index ["code"], name: "index_spree_menu_items_on_code"
    t.index ["depth"], name: "index_spree_menu_items_on_depth"
    t.index ["item_type"], name: "index_spree_menu_items_on_item_type"
    t.index ["lft"], name: "index_spree_menu_items_on_lft"
    t.index ["linked_resource_type", "linked_resource_id"], name: "index_spree_menu_items_on_linked_resource"
    t.index ["menu_id"], name: "index_spree_menu_items_on_menu_id"
    t.index ["parent_id"], name: "index_spree_menu_items_on_parent_id"
    t.index ["rgt"], name: "index_spree_menu_items_on_rgt"
  end

  create_table "spree_menus", force: :cascade do |t|
    t.string "name"
    t.string "location"
    t.string "locale"
    t.bigint "store_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["locale"], name: "index_spree_menus_on_locale"
    t.index ["store_id", "location", "locale"], name: "index_spree_menus_on_store_id_and_location_and_locale", unique: true
    t.index ["store_id"], name: "index_spree_menus_on_store_id"
  end

  create_table "spree_metabase_report_settings", force: :cascade do |t|
    t.bigint "organisation_id"
    t.bigint "store_id"
    t.bigint "user_id"
    t.string "metabase_sharing_type", default: "dashboard"
    t.string "metabase_sharing_data"
    t.string "root_menu_name", default: "Metabase Report"
    t.string "report_menu_name"
    t.jsonb "report_params"
    t.boolean "report_enabled", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["organisation_id"], name: "index_spree_metabase_report_settings_on_organisation_id"
    t.index ["store_id"], name: "index_spree_metabase_report_settings_on_store_id"
    t.index ["user_id"], name: "index_spree_metabase_report_settings_on_user_id"
  end

  create_table "spree_metabase_settings", force: :cascade do |t|
    t.integer "organisation_id"
    t.bigint "store_id"
    t.string "metabase_site_url"
    t.string "metabase_secret_key"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["organisation_id"], name: "index_spree_metabase_settings_on_organisation_id", unique: true
    t.index ["store_id"], name: "index_spree_metabase_settings_on_store_id"
  end

  create_table "spree_oauth_access_grants", force: :cascade do |t|
    t.bigint "resource_owner_id", null: false
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.integer "expires_in", null: false
    t.text "redirect_uri", null: false
    t.datetime "created_at", null: false
    t.datetime "revoked_at"
    t.string "scopes"
    t.string "resource_owner_type", null: false
    t.index ["application_id"], name: "index_spree_oauth_access_grants_on_application_id"
    t.index ["resource_owner_id", "resource_owner_type"], name: "polymorphic_owner_oauth_access_grants"
    t.index ["token"], name: "index_spree_oauth_access_grants_on_token", unique: true
  end

  create_table "spree_oauth_access_tokens", force: :cascade do |t|
    t.bigint "resource_owner_id"
    t.bigint "application_id"
    t.string "token", null: false
    t.string "refresh_token"
    t.integer "expires_in"
    t.datetime "revoked_at"
    t.datetime "created_at", null: false
    t.string "scopes"
    t.string "previous_refresh_token", default: "", null: false
    t.string "resource_owner_type"
    t.datetime "expire_date"
    t.string "grantless_access_token"
    t.index ["application_id"], name: "index_spree_oauth_access_tokens_on_application_id"
    t.index ["refresh_token"], name: "index_spree_oauth_access_tokens_on_refresh_token", unique: true
    t.index ["resource_owner_id", "resource_owner_type"], name: "polymorphic_owner_oauth_access_tokens"
    t.index ["resource_owner_id"], name: "index_spree_oauth_access_tokens_on_resource_owner_id"
    t.index ["token"], name: "index_spree_oauth_access_tokens_on_token", unique: true
  end

  create_table "spree_oauth_applications", force: :cascade do |t|
    t.string "name", null: false
    t.string "uid", null: false
    t.string "secret", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.boolean "confidential", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "site_id"
    t.bigint "sale_channel_id"
    t.bigint "store_id"
    t.string "seller_name"
    t.boolean "re_authenticate", default: false
    t.integer "re_authenticate_reason"
    t.index ["sale_channel_id"], name: "index_spree_oauth_applications_on_sale_channel_id"
    t.index ["store_id"], name: "index_spree_oauth_applications_on_store_id"
    t.index ["uid"], name: "index_spree_oauth_applications_on_uid", unique: true
  end

  create_table "spree_option_type_prototypes", force: :cascade do |t|
    t.bigint "prototype_id"
    t.bigint "option_type_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["option_type_id"], name: "index_spree_option_type_prototypes_on_option_type_id"
    t.index ["prototype_id", "option_type_id"], name: "spree_option_type_prototypes_prototype_id_option_type_id", unique: true
    t.index ["prototype_id"], name: "index_spree_option_type_prototypes_on_prototype_id"
  end

  create_table "spree_option_type_translations", force: :cascade do |t|
    t.string "name"
    t.string "presentation"
    t.string "locale", null: false
    t.bigint "spree_option_type_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["locale"], name: "index_spree_option_type_translations_on_locale"
    t.index ["spree_option_type_id", "locale"], name: "unique_option_type_id_per_locale", unique: true
  end

  create_table "spree_option_types", force: :cascade do |t|
    t.string "name", limit: 100
    t.string "presentation", limit: 100
    t.integer "position", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "filterable", default: true, null: false
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.bigint "store_id"
    t.index ["filterable"], name: "index_spree_option_types_on_filterable"
    t.index ["name"], name: "index_spree_option_types_on_name"
    t.index ["position"], name: "index_spree_option_types_on_position"
    t.index ["store_id"], name: "index_spree_option_types_on_store_id"
  end

  create_table "spree_option_value_translations", force: :cascade do |t|
    t.string "name"
    t.string "presentation"
    t.string "locale", null: false
    t.bigint "spree_option_value_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["locale"], name: "index_spree_option_value_translations_on_locale"
    t.index ["spree_option_value_id", "locale"], name: "unique_option_value_id_per_locale", unique: true
  end

  create_table "spree_option_value_variants", force: :cascade do |t|
    t.bigint "variant_id"
    t.bigint "option_value_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["option_value_id"], name: "index_spree_option_value_variants_on_option_value_id"
    t.index ["variant_id", "option_value_id"], name: "index_option_values_variants_on_variant_id_and_option_value_id", unique: true
    t.index ["variant_id"], name: "index_spree_option_value_variants_on_variant_id"
  end

  create_table "spree_option_values", force: :cascade do |t|
    t.integer "position"
    t.string "name"
    t.string "presentation"
    t.bigint "option_type_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.index ["name"], name: "index_spree_option_values_on_name"
    t.index ["option_type_id"], name: "index_spree_option_values_on_option_type_id"
    t.index ["position"], name: "index_spree_option_values_on_position"
  end

  create_table "spree_order_invoices", force: :cascade do |t|
    t.string "number", limit: 16
    t.bigint "order_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["order_id"], name: "index_spree_order_invoices_on_order_id"
  end

  create_table "spree_order_packages", force: :cascade do |t|
    t.string "number"
    t.string "tracking"
    t.string "tracking_label"
    t.string "state", default: "ready"
    t.integer "selected_shipping_rate_id"
    t.bigint "order_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "insure", default: false
    t.decimal "insure_for_amount", precision: 10, scale: 2
    t.decimal "cost_to_insure", precision: 10, scale: 5
    t.string "shipping_source"
    t.bigint "easypost_setting_id"
    t.index ["easypost_setting_id"], name: "index_spree_order_packages_on_easypost_setting_id"
    t.index ["order_id"], name: "index_spree_order_packages_on_order_id"
  end

  create_table "spree_order_promotions", force: :cascade do |t|
    t.bigint "order_id"
    t.bigint "promotion_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["order_id"], name: "index_spree_order_promotions_on_order_id"
    t.index ["promotion_id", "order_id"], name: "index_spree_order_promotions_on_promotion_id_and_order_id"
    t.index ["promotion_id"], name: "index_spree_order_promotions_on_promotion_id"
  end

  create_table "spree_ordered_listings_infos", force: :cascade do |t|
    t.jsonb "listing_quantities"
    t.decimal "weight", precision: 10, scale: 2
    t.decimal "length", precision: 10, scale: 2
    t.decimal "width", precision: 10, scale: 2
    t.decimal "height", precision: 10, scale: 2
    t.integer "shipping_method_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "spree_orders", force: :cascade do |t|
    t.string "number", limit: 32
    t.decimal "item_total", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "total", precision: 10, scale: 2, default: "0.0", null: false
    t.string "state"
    t.decimal "adjustment_total", precision: 10, scale: 2, default: "0.0", null: false
    t.bigint "user_id"
    t.datetime "completed_at"
    t.bigint "bill_address_id"
    t.bigint "ship_address_id"
    t.decimal "payment_total", precision: 10, scale: 2, default: "0.0"
    t.string "shipment_state"
    t.string "payment_state"
    t.string "email"
    t.text "special_instructions"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "currency"
    t.string "last_ip_address"
    t.bigint "created_by_id"
    t.decimal "shipment_total", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "additional_tax_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "promo_total", precision: 10, scale: 2, default: "0.0"
    t.string "channel", default: "AXEL Market"
    t.decimal "included_tax_total", precision: 10, scale: 2, default: "0.0", null: false
    t.integer "item_count", default: 0
    t.bigint "approver_id"
    t.datetime "approved_at"
    t.boolean "confirmation_delivered", default: false
    t.boolean "considered_risky", default: false
    t.string "token"
    t.datetime "canceled_at"
    t.bigint "canceler_id"
    t.bigint "store_id"
    t.integer "state_lock_version", default: 0, null: false
    t.decimal "taxable_adjustment_total", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "non_taxable_adjustment_total", precision: 10, scale: 2, default: "0.0", null: false
    t.boolean "store_owner_notification_delivered"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.string "last_modified_date"
    t.jsonb "sale_channel_metadata"
    t.integer "sale_channel_id"
    t.text "complimentary_items"
    t.decimal "envelope_fee"
    t.jsonb "ebay_fees", default: {}
    t.boolean "local_pickup", default: false
    t.jsonb "selected_shipping_method"
    t.string "uploaded_by"
    t.text "internal_note"
    t.boolean "subscription_order", default: false, null: false
    t.jsonb "walmart_fees", default: {}
    t.index ["approver_id"], name: "index_spree_orders_on_approver_id"
    t.index ["bill_address_id"], name: "index_spree_orders_on_bill_address_id"
    t.index ["canceler_id"], name: "index_spree_orders_on_canceler_id"
    t.index ["completed_at"], name: "index_spree_orders_on_completed_at"
    t.index ["confirmation_delivered"], name: "index_spree_orders_on_confirmation_delivered"
    t.index ["considered_risky"], name: "index_spree_orders_on_considered_risky"
    t.index ["created_by_id"], name: "index_spree_orders_on_created_by_id"
    t.index ["item_total"], name: "index_spree_orders_on_item_total"
    t.index ["number"], name: "index_spree_orders_on_number", unique: true
    t.index ["ship_address_id"], name: "index_spree_orders_on_ship_address_id"
    t.index ["store_id"], name: "index_spree_orders_on_store_id"
    t.index ["token"], name: "index_spree_orders_on_token"
    t.index ["user_id", "created_by_id"], name: "index_spree_orders_on_user_id_and_created_by_id"
  end

  create_table "spree_orders_subscriptions", id: false, force: :cascade do |t|
    t.bigint "order_id"
    t.bigint "subscription_id"
  end

  create_table "spree_organisations", force: :cascade do |t|
    t.string "name"
    t.string "subdomain"
    t.string "admin_email"
    t.string "url"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "custom_domain"
    t.string "flag"
    t.boolean "two_factor_enabled", default: false
  end

  create_table "spree_organisations_admin_users", force: :cascade do |t|
    t.bigint "organisation_id", null: false
    t.bigint "admin_user_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["organisation_id", "admin_user_id"], name: "index_unique_org_admin_user", unique: true
  end

  create_table "spree_payment_capture_events", force: :cascade do |t|
    t.decimal "amount", precision: 10, scale: 2, default: "0.0"
    t.bigint "payment_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["payment_id"], name: "index_spree_payment_capture_events_on_payment_id"
  end

  create_table "spree_payment_methods", force: :cascade do |t|
    t.string "type"
    t.string "name"
    t.text "description"
    t.boolean "active", default: true
    t.datetime "deleted_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "display_on", default: "both"
    t.boolean "auto_capture"
    t.text "preferences"
    t.integer "position", default: 0
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.jsonb "settings"
    t.index ["id", "type"], name: "index_spree_payment_methods_on_id_and_type"
    t.index ["id"], name: "index_spree_payment_methods_on_id"
  end

  create_table "spree_payment_methods_stores", id: false, force: :cascade do |t|
    t.bigint "payment_method_id"
    t.bigint "store_id"
    t.index ["payment_method_id", "store_id"], name: "payment_mentod_id_store_id_unique_index", unique: true
    t.index ["payment_method_id"], name: "index_spree_payment_methods_stores_on_payment_method_id"
    t.index ["store_id"], name: "index_spree_payment_methods_stores_on_store_id"
  end

  create_table "spree_payment_sources", force: :cascade do |t|
    t.string "gateway_payment_profile_id"
    t.string "type"
    t.bigint "payment_method_id"
    t.bigint "user_id"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["payment_method_id"], name: "index_spree_payment_sources_on_payment_method_id"
    t.index ["type", "gateway_payment_profile_id"], name: "index_payment_sources_on_type_and_gateway_payment_profile_id", unique: true
    t.index ["type"], name: "index_spree_payment_sources_on_type"
    t.index ["user_id"], name: "index_spree_payment_sources_on_user_id"
  end

  create_table "spree_payments", force: :cascade do |t|
    t.decimal "amount", precision: 10, scale: 2, default: "0.0", null: false
    t.bigint "order_id"
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "payment_method_id"
    t.string "state"
    t.string "response_code"
    t.string "avs_response"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "number"
    t.string "cvv_response_code"
    t.string "cvv_response_message"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.string "intent_client_key"
    t.string "paypal_fees"
    t.string "stripe_fees"
    t.index ["number"], name: "index_spree_payments_on_number", unique: true
    t.index ["order_id"], name: "index_spree_payments_on_order_id"
    t.index ["payment_method_id"], name: "index_spree_payments_on_payment_method_id"
    t.index ["source_id", "source_type"], name: "index_spree_payments_on_source_id_and_source_type"
  end

  create_table "spree_paypal_express_checkouts", id: :serial, force: :cascade do |t|
    t.string "token"
    t.string "payer_id"
    t.string "transaction_id"
    t.string "state", default: "complete"
    t.string "refund_transaction_id"
    t.datetime "refunded_at"
    t.string "refund_type"
    t.datetime "created_at"
    t.index ["transaction_id"], name: "index_spree_paypal_express_checkouts_on_transaction_id"
  end

  create_table "spree_paypal_express_requests", force: :cascade do |t|
    t.string "token"
    t.boolean "success"
    t.string "state"
    t.jsonb "response"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "order_id"
    t.bigint "payment_method_id"
    t.index ["order_id"], name: "index_spree_paypal_express_requests_on_order_id"
    t.index ["payment_method_id"], name: "index_spree_paypal_express_requests_on_payment_method_id"
    t.index ["token"], name: "index_spree_paypal_express_requests_on_token"
  end

  create_table "spree_permissions", force: :cascade do |t|
    t.bigint "role_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "create_and_edit_product"
    t.boolean "view_finance_and_sales"
    t.boolean "allow_product_delete"
    t.boolean "view_product_cost_price"
    t.boolean "manage_stock"
    t.boolean "create_and_edit_listing"
    t.boolean "manage_sale_channel"
    t.boolean "edit_listing"
    t.boolean "create_and_edit_order"
    t.boolean "create_and_edit_users"
    t.boolean "send_email"
    t.boolean "delete_users"
    t.boolean "view_activity_log"
    t.boolean "sale_and_finance_report"
    t.boolean "product_and_inventory_report"
    t.boolean "create_and_edit_store"
    t.boolean "publish_unpublish_store"
    t.boolean "delete_store"
    t.boolean "manage_store_payment_configuration"
    t.boolean "manage_webhooks"
    t.boolean "manage_roles"
    t.boolean "show_dashboard"
    t.boolean "show_inventory"
    t.boolean "manage_listings"
    t.boolean "manage_orders"
    t.boolean "manage_users"
    t.boolean "access_reports"
    t.boolean "store_settings"
    t.boolean "general_settings"
    t.boolean "manage_blogs"
    t.boolean "view_blogs"
    t.boolean "create_and_edit_blogs"
    t.boolean "delete_blogs"
    t.index ["role_id"], name: "index_spree_permissions_on_role_id"
  end

  create_table "spree_preferences", force: :cascade do |t|
    t.text "value"
    t.string "key"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["key"], name: "index_spree_preferences_on_key", unique: true
  end

  create_table "spree_price_histories", force: :cascade do |t|
    t.integer "variant_id", null: false
    t.string "currency"
    t.decimal "amount", precision: 10, scale: 2
    t.decimal "previous_amount", precision: 10, scale: 2
    t.string "kind"
    t.string "strategy"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["variant_id"], name: "index_spree_price_histories_on_variant_id"
  end

  create_table "spree_prices", force: :cascade do |t|
    t.bigint "variant_id", null: false
    t.decimal "amount", precision: 10, scale: 2
    t.string "currency"
    t.datetime "deleted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "compare_at_amount", precision: 10, scale: 2
    t.decimal "compare_to_amount", precision: 10, scale: 2
    t.index ["deleted_at"], name: "index_spree_prices_on_deleted_at"
    t.index ["variant_id", "currency"], name: "index_spree_prices_on_variant_id_and_currency"
    t.index ["variant_id"], name: "index_spree_prices_on_variant_id"
  end

  create_table "spree_product_notifications", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "variant_id", null: false
    t.string "email", null: false
    t.boolean "notify", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "action"
    t.index ["user_id"], name: "index_spree_product_notifications_on_user_id"
    t.index ["variant_id"], name: "index_spree_product_notifications_on_variant_id"
  end

  create_table "spree_product_option_types", force: :cascade do |t|
    t.integer "position"
    t.bigint "product_id"
    t.bigint "option_type_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["option_type_id"], name: "index_spree_product_option_types_on_option_type_id"
    t.index ["position"], name: "index_spree_product_option_types_on_position"
    t.index ["product_id"], name: "index_spree_product_option_types_on_product_id"
  end

  create_table "spree_product_packages", id: :serial, force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "length", default: 0, null: false
    t.integer "width", default: 0, null: false
    t.integer "height", default: 0, null: false
    t.integer "weight", default: 0, null: false
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "spree_product_promotion_rules", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "promotion_rule_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["product_id"], name: "index_products_promotion_rules_on_product_id"
    t.index ["promotion_rule_id", "product_id"], name: "index_products_promotion_rules_on_promotion_rule_and_product"
  end

  create_table "spree_product_properties", force: :cascade do |t|
    t.string "value"
    t.bigint "product_id"
    t.bigint "property_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "position", default: 0
    t.boolean "show_property", default: true
    t.string "filter_param"
    t.index ["filter_param"], name: "index_spree_product_properties_on_filter_param"
    t.index ["position"], name: "index_spree_product_properties_on_position"
    t.index ["product_id"], name: "index_product_properties_on_product_id"
    t.index ["property_id", "product_id"], name: "index_spree_product_properties_on_property_id_and_product_id", unique: true
    t.index ["property_id"], name: "index_spree_product_properties_on_property_id"
  end

  create_table "spree_product_property_translations", force: :cascade do |t|
    t.string "value"
    t.string "filter_param"
    t.string "locale", null: false
    t.bigint "spree_product_property_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["locale"], name: "index_spree_product_property_translations_on_locale"
    t.index ["spree_product_property_id", "locale"], name: "unique_product_property_id_per_locale", unique: true
  end

  create_table "spree_product_translations", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.string "locale", null: false
    t.bigint "spree_product_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.text "meta_description"
    t.string "meta_keywords"
    t.string "meta_title"
    t.string "slug"
    t.datetime "deleted_at"
    t.index ["deleted_at"], name: "index_spree_product_translations_on_deleted_at"
    t.index ["locale", "slug"], name: "unique_slug_per_locale", unique: true
    t.index ["locale"], name: "index_spree_product_translations_on_locale"
    t.index ["spree_product_id", "locale"], name: "unique_product_id_per_locale", unique: true
  end

  create_table "spree_products", force: :cascade do |t|
    t.string "name", default: ""
    t.text "description"
    t.datetime "available_on"
    t.datetime "deleted_at"
    t.string "slug"
    t.text "meta_description"
    t.string "meta_keywords"
    t.bigint "tax_category_id"
    t.bigint "shipping_category_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "promotionable", default: true
    t.string "meta_title"
    t.datetime "discontinue_on"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.string "easy_post_hs_tariff_number"
    t.boolean "active_old", default: false
    t.jsonb "ebay_item_specifics"
    t.boolean "temporary_unavailable", default: false
    t.jsonb "upc_item_db_data"
    t.bigint "scanned_item_id"
    t.string "abbreviation"
    t.decimal "lbs"
    t.decimal "oz"
    t.string "uploaded_by"
    t.string "status", default: "draft", null: false
    t.datetime "make_active_at"
    t.index ["available_on"], name: "index_spree_products_on_available_on"
    t.index ["deleted_at"], name: "index_spree_products_on_deleted_at"
    t.index ["discontinue_on"], name: "index_spree_products_on_discontinue_on"
    t.index ["make_active_at"], name: "index_spree_products_on_make_active_at"
    t.index ["name"], name: "index_spree_products_on_name"
    t.index ["scanned_item_id"], name: "index_spree_products_on_scanned_item_id"
    t.index ["shipping_category_id"], name: "index_spree_products_on_shipping_category_id"
    t.index ["slug"], name: "index_spree_products_on_slug", unique: true
    t.index ["status", "deleted_at"], name: "index_spree_products_on_status_and_deleted_at"
    t.index ["status"], name: "index_spree_products_on_status"
    t.index ["tax_category_id"], name: "index_spree_products_on_tax_category_id"
    t.index ["updated_at"], name: "index_spree_products_on_updated_at"
  end

  create_table "spree_products_stores", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "store_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id", "store_id"], name: "index_spree_products_stores_on_product_id_and_store_id", unique: true
    t.index ["product_id"], name: "index_spree_products_stores_on_product_id"
    t.index ["store_id"], name: "index_spree_products_stores_on_store_id"
  end

  create_table "spree_products_taxons", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "taxon_id"
    t.integer "position"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["position"], name: "index_spree_products_taxons_on_position"
    t.index ["product_id", "taxon_id"], name: "index_spree_products_taxons_on_product_id_and_taxon_id", unique: true
    t.index ["product_id"], name: "index_spree_products_taxons_on_product_id"
    t.index ["taxon_id"], name: "index_spree_products_taxons_on_taxon_id"
  end

  create_table "spree_promotion_action_line_items", force: :cascade do |t|
    t.bigint "promotion_action_id"
    t.bigint "variant_id"
    t.integer "quantity", default: 1
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["promotion_action_id"], name: "index_spree_promotion_action_line_items_on_promotion_action_id"
    t.index ["variant_id"], name: "index_spree_promotion_action_line_items_on_variant_id"
  end

  create_table "spree_promotion_actions", force: :cascade do |t|
    t.bigint "promotion_id"
    t.integer "position"
    t.string "type"
    t.datetime "deleted_at"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["deleted_at"], name: "index_spree_promotion_actions_on_deleted_at"
    t.index ["id", "type"], name: "index_spree_promotion_actions_on_id_and_type"
    t.index ["promotion_id"], name: "index_spree_promotion_actions_on_promotion_id"
  end

  create_table "spree_promotion_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "code"
  end

  create_table "spree_promotion_rule_taxons", force: :cascade do |t|
    t.bigint "taxon_id"
    t.bigint "promotion_rule_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["promotion_rule_id"], name: "index_spree_promotion_rule_taxons_on_promotion_rule_id"
    t.index ["taxon_id"], name: "index_spree_promotion_rule_taxons_on_taxon_id"
  end

  create_table "spree_promotion_rule_users", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "promotion_rule_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["promotion_rule_id"], name: "index_promotion_rules_users_on_promotion_rule_id"
    t.index ["user_id", "promotion_rule_id"], name: "index_promotion_rules_users_on_user_id_and_promotion_rule_id"
  end

  create_table "spree_promotion_rules", force: :cascade do |t|
    t.bigint "promotion_id"
    t.bigint "user_id"
    t.bigint "product_group_id"
    t.string "type"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "code"
    t.text "preferences"
    t.index ["product_group_id"], name: "index_promotion_rules_on_product_group_id"
    t.index ["promotion_id"], name: "index_spree_promotion_rules_on_promotion_id"
    t.index ["user_id"], name: "index_promotion_rules_on_user_id"
  end

  create_table "spree_promotions", force: :cascade do |t|
    t.string "description"
    t.datetime "expires_at"
    t.datetime "starts_at"
    t.string "name"
    t.string "type"
    t.integer "usage_limit"
    t.string "match_policy", default: "all"
    t.string "code"
    t.boolean "advertise", default: false
    t.string "path"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "promotion_category_id"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.index ["advertise"], name: "index_spree_promotions_on_advertise"
    t.index ["code"], name: "index_spree_promotions_on_code"
    t.index ["expires_at"], name: "index_spree_promotions_on_expires_at"
    t.index ["id", "type"], name: "index_spree_promotions_on_id_and_type"
    t.index ["path"], name: "index_spree_promotions_on_path"
    t.index ["promotion_category_id"], name: "index_spree_promotions_on_promotion_category_id"
    t.index ["starts_at"], name: "index_spree_promotions_on_starts_at"
  end

  create_table "spree_promotions_stores", force: :cascade do |t|
    t.bigint "promotion_id"
    t.bigint "store_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["promotion_id", "store_id"], name: "index_spree_promotions_stores_on_promotion_id_and_store_id", unique: true
    t.index ["promotion_id"], name: "index_spree_promotions_stores_on_promotion_id"
    t.index ["store_id"], name: "index_spree_promotions_stores_on_store_id"
  end

  create_table "spree_properties", force: :cascade do |t|
    t.string "name"
    t.string "presentation"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "filterable", default: false, null: false
    t.string "filter_param"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.bigint "store_id"
    t.index ["filter_param"], name: "index_spree_properties_on_filter_param"
    t.index ["filterable"], name: "index_spree_properties_on_filterable"
    t.index ["name"], name: "index_spree_properties_on_name"
    t.index ["store_id"], name: "index_spree_properties_on_store_id"
  end

  create_table "spree_property_prototypes", force: :cascade do |t|
    t.bigint "prototype_id"
    t.bigint "property_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["property_id"], name: "index_spree_property_prototypes_on_property_id"
    t.index ["prototype_id", "property_id"], name: "index_property_prototypes_on_prototype_id_and_property_id", unique: true
    t.index ["prototype_id"], name: "index_spree_property_prototypes_on_prototype_id"
  end

  create_table "spree_property_translations", force: :cascade do |t|
    t.string "name"
    t.string "presentation"
    t.string "filter_param"
    t.string "locale", null: false
    t.bigint "spree_property_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["locale"], name: "index_spree_property_translations_on_locale"
    t.index ["spree_property_id", "locale"], name: "unique_property_id_per_locale", unique: true
  end

  create_table "spree_prototype_taxons", force: :cascade do |t|
    t.bigint "taxon_id"
    t.bigint "prototype_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["prototype_id", "taxon_id"], name: "index_spree_prototype_taxons_on_prototype_id_and_taxon_id"
    t.index ["prototype_id"], name: "index_spree_prototype_taxons_on_prototype_id"
    t.index ["taxon_id"], name: "index_spree_prototype_taxons_on_taxon_id"
  end

  create_table "spree_prototypes", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.bigint "store_id"
    t.index ["store_id"], name: "index_spree_prototypes_on_store_id"
  end

  create_table "spree_recent_thirty_days_sales_records", force: :cascade do |t|
    t.bigint "product_id"
    t.integer "quantity", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_spree_recent_thirty_days_sales_records_on_product_id", unique: true
    t.index ["quantity"], name: "index_spree_recent_thirty_days_sales_records_on_quantity"
  end

  create_table "spree_recommended_prices", force: :cascade do |t|
    t.integer "variant_id", null: false
    t.string "status", default: "wait", null: false
    t.decimal "current_price", precision: 10, scale: 2
    t.decimal "zero_profit_price", precision: 10, scale: 2
    t.decimal "average_price", precision: 10, scale: 2
    t.decimal "average_effective_price", precision: 10, scale: 2
    t.jsonb "prices", default: {}, null: false
    t.string "external_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "error_message"
    t.jsonb "request_data", default: {}
    t.string "request"
    t.string "response"
    t.decimal "min_price_ebay", precision: 10, scale: 2
    t.decimal "weighted_average_effective", precision: 10, scale: 2
    t.boolean "auto_update", default: false, null: false
    t.integer "auto_update_period"
    t.jsonb "settings", default: {}, null: false
    t.decimal "shipping_cost", precision: 10, scale: 2
    t.datetime "auto_update_at"
    t.datetime "data_updated_at"
    t.integer "listing_inventory_id"
    t.uuid "analysis_id"
    t.string "stocks_rate_value"
    t.string "sales_rate_value"
    t.string "sales_period"
    t.index ["variant_id"], name: "index_spree_recommended_prices_on_variant_id"
  end

  create_table "spree_refund_reasons", force: :cascade do |t|
    t.string "name"
    t.boolean "active", default: true
    t.boolean "mutable", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["name"], name: "index_spree_refund_reasons_on_name", unique: true
  end

  create_table "spree_refunds", force: :cascade do |t|
    t.bigint "payment_id"
    t.decimal "amount", precision: 10, scale: 2, default: "0.0", null: false
    t.string "transaction_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "refund_reason_id"
    t.bigint "reimbursement_id"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.bigint "refund_reference_id"
    t.index ["payment_id"], name: "index_spree_refunds_on_payment_id"
    t.index ["refund_reason_id"], name: "index_refunds_on_refund_reason_id"
    t.index ["reimbursement_id"], name: "index_spree_refunds_on_reimbursement_id"
  end

  create_table "spree_reimbursement_credits", force: :cascade do |t|
    t.decimal "amount", precision: 10, scale: 2, default: "0.0", null: false
    t.bigint "reimbursement_id"
    t.bigint "creditable_id"
    t.string "creditable_type"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["creditable_id", "creditable_type"], name: "index_reimbursement_credits_on_creditable_id_and_type"
    t.index ["reimbursement_id"], name: "index_spree_reimbursement_credits_on_reimbursement_id"
  end

  create_table "spree_reimbursement_types", force: :cascade do |t|
    t.string "name"
    t.boolean "active", default: true
    t.boolean "mutable", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "type"
    t.index ["name"], name: "index_spree_reimbursement_types_on_name", unique: true
    t.index ["type"], name: "index_spree_reimbursement_types_on_type"
  end

  create_table "spree_reimbursements", force: :cascade do |t|
    t.string "number"
    t.string "reimbursement_status"
    t.bigint "customer_return_id"
    t.bigint "order_id"
    t.decimal "total", precision: 10, scale: 2
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["customer_return_id"], name: "index_spree_reimbursements_on_customer_return_id"
    t.index ["number"], name: "index_spree_reimbursements_on_number", unique: true
    t.index ["order_id"], name: "index_spree_reimbursements_on_order_id"
  end

  create_table "spree_return_authorization_reasons", force: :cascade do |t|
    t.string "name"
    t.boolean "active", default: true
    t.boolean "mutable", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["name"], name: "index_spree_return_authorization_reasons_on_name", unique: true
  end

  create_table "spree_return_authorizations", force: :cascade do |t|
    t.string "number"
    t.string "state"
    t.bigint "order_id"
    t.text "memo"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.bigint "stock_location_id"
    t.bigint "return_authorization_reason_id"
    t.boolean "refund_shipping_fee", default: false
    t.boolean "authorized", default: false
    t.index ["number"], name: "index_spree_return_authorizations_on_number", unique: true
    t.index ["order_id"], name: "index_spree_return_authorizations_on_order_id"
    t.index ["return_authorization_reason_id"], name: "index_return_authorizations_on_return_authorization_reason_id"
    t.index ["stock_location_id"], name: "index_spree_return_authorizations_on_stock_location_id"
  end

  create_table "spree_return_items", force: :cascade do |t|
    t.bigint "return_authorization_id"
    t.bigint "inventory_unit_id"
    t.bigint "exchange_variant_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "pre_tax_amount", precision: 12, scale: 4, default: "0.0", null: false
    t.decimal "included_tax_total", precision: 12, scale: 4, default: "0.0", null: false
    t.decimal "additional_tax_total", precision: 12, scale: 4, default: "0.0", null: false
    t.string "reception_status"
    t.string "acceptance_status"
    t.bigint "customer_return_id"
    t.bigint "reimbursement_id"
    t.text "acceptance_status_errors"
    t.bigint "preferred_reimbursement_type_id"
    t.bigint "override_reimbursement_type_id"
    t.boolean "resellable", default: true, null: false
    t.text "reason"
    t.bigint "return_authorization_reason_id"
    t.index ["customer_return_id"], name: "index_return_items_on_customer_return_id"
    t.index ["exchange_variant_id"], name: "index_spree_return_items_on_exchange_variant_id"
    t.index ["inventory_unit_id"], name: "index_spree_return_items_on_inventory_unit_id"
    t.index ["override_reimbursement_type_id"], name: "index_spree_return_items_on_override_reimbursement_type_id"
    t.index ["preferred_reimbursement_type_id"], name: "index_spree_return_items_on_preferred_reimbursement_type_id"
    t.index ["reimbursement_id"], name: "index_spree_return_items_on_reimbursement_id"
    t.index ["return_authorization_id"], name: "index_spree_return_items_on_return_authorization_id"
    t.index ["return_authorization_reason_id"], name: "index_spree_return_items_on_return_authorization_reason_id"
  end

  create_table "spree_role_users", force: :cascade do |t|
    t.bigint "role_id"
    t.bigint "user_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.bigint "store_id"
    t.index ["role_id"], name: "index_spree_role_users_on_role_id"
    t.index ["store_id"], name: "index_spree_role_users_on_store_id"
    t.index ["user_id"], name: "index_spree_role_users_on_user_id"
  end

  create_table "spree_roles", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["name"], name: "index_spree_roles_on_name", unique: true
  end

  create_table "spree_sale_channels", force: :cascade do |t|
    t.string "brand"
    t.string "slug"
    t.string "description"
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "email_template_id"
    t.index ["store_id"], name: "index_spree_sale_channels_on_store_id"
  end

  create_table "spree_scan_forms", force: :cascade do |t|
    t.string "easy_post_scan_form_id"
    t.bigint "stock_location_id"
    t.text "scan_form"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "number"
    t.bigint "store_id"
    t.index ["stock_location_id"], name: "index_spree_scan_forms_on_stock_location_id"
    t.index ["store_id"], name: "index_spree_scan_forms_on_store_id"
  end

  create_table "spree_scanned_items", force: :cascade do |t|
    t.string "title"
    t.string "upc"
    t.string "ean"
    t.string "description"
    t.string "brand"
    t.string "model"
    t.string "currency"
    t.jsonb "images"
    t.jsonb "upc_data"
    t.bigint "user_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "status"
    t.index ["user_id"], name: "index_spree_scanned_items_on_user_id"
  end

  create_table "spree_shipments", force: :cascade do |t|
    t.string "tracking"
    t.string "number"
    t.decimal "cost", precision: 10, scale: 2, default: "0.0"
    t.datetime "shipped_at"
    t.bigint "order_id"
    t.bigint "address_id"
    t.string "state"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "stock_location_id"
    t.decimal "adjustment_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "additional_tax_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "promo_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "included_tax_total", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "pre_tax_amount", precision: 12, scale: 4, default: "0.0", null: false
    t.decimal "taxable_adjustment_total", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "non_taxable_adjustment_total", precision: 10, scale: 2, default: "0.0", null: false
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.text "tracking_label"
    t.bigint "scan_form_id"
    t.decimal "buyer_paid_amount", precision: 8, scale: 2
    t.datetime "deleted_at"
    t.bigint "order_package_id"
    t.integer "email_template_id"
    t.string "custom_message"
    t.boolean "insure", default: false
    t.decimal "insure_for_amount", precision: 10, scale: 2
    t.decimal "cost_to_insure", precision: 10, scale: 5
    t.index ["address_id"], name: "index_spree_shipments_on_address_id"
    t.index ["number"], name: "index_spree_shipments_on_number", unique: true
    t.index ["order_id"], name: "index_spree_shipments_on_order_id"
    t.index ["order_package_id"], name: "index_spree_shipments_on_order_package_id"
    t.index ["scan_form_id"], name: "index_spree_shipments_on_scan_form_id"
    t.index ["stock_location_id"], name: "index_spree_shipments_on_stock_location_id"
  end

  create_table "spree_shipping_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "store_id"
    t.boolean "use_easypost", default: false
    t.index ["name"], name: "index_spree_shipping_categories_on_name"
    t.index ["store_id"], name: "index_spree_shipping_categories_on_store_id"
  end

  create_table "spree_shipping_method_categories", force: :cascade do |t|
    t.bigint "shipping_method_id", null: false
    t.bigint "shipping_category_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["shipping_category_id", "shipping_method_id"], name: "unique_spree_shipping_method_categories", unique: true
    t.index ["shipping_category_id"], name: "index_spree_shipping_method_categories_on_shipping_category_id"
    t.index ["shipping_method_id"], name: "index_spree_shipping_method_categories_on_shipping_method_id"
  end

  create_table "spree_shipping_method_zones", force: :cascade do |t|
    t.bigint "shipping_method_id"
    t.bigint "zone_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["shipping_method_id"], name: "index_spree_shipping_method_zones_on_shipping_method_id"
    t.index ["zone_id"], name: "index_spree_shipping_method_zones_on_zone_id"
  end

  create_table "spree_shipping_methods", force: :cascade do |t|
    t.string "name"
    t.string "display_on"
    t.datetime "deleted_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "tracking_url"
    t.string "admin_name"
    t.bigint "tax_category_id"
    t.string "code"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.bigint "store_id"
    t.boolean "local_pickup", default: false
    t.text "address_details"
    t.string "pickup_zip"
    t.string "pickup_state"
    t.string "pickup_country"
    t.index ["deleted_at"], name: "index_spree_shipping_methods_on_deleted_at"
    t.index ["store_id"], name: "index_spree_shipping_methods_on_store_id"
    t.index ["tax_category_id"], name: "index_spree_shipping_methods_on_tax_category_id"
  end

  create_table "spree_shipping_rates", force: :cascade do |t|
    t.bigint "shipment_id"
    t.bigint "shipping_method_id"
    t.boolean "selected", default: false
    t.decimal "cost", precision: 8, scale: 2, default: "0.0"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "tax_rate_id"
    t.string "easy_post_shipment_id"
    t.string "easy_post_rate_id"
    t.boolean "buyer_selected", default: false
    t.bigint "order_package_id"
    t.index ["order_package_id"], name: "index_spree_shipping_rates_on_order_package_id"
    t.index ["selected"], name: "index_spree_shipping_rates_on_selected"
    t.index ["shipment_id", "shipping_method_id"], name: "spree_shipping_rates_join_index", unique: true
    t.index ["shipment_id"], name: "index_spree_shipping_rates_on_shipment_id"
    t.index ["shipping_method_id"], name: "index_spree_shipping_rates_on_shipping_method_id"
    t.index ["tax_rate_id"], name: "index_spree_shipping_rates_on_tax_rate_id"
  end

  create_table "spree_state_changes", force: :cascade do |t|
    t.string "name"
    t.string "previous_state"
    t.bigint "stateful_id"
    t.bigint "user_id"
    t.string "stateful_type"
    t.string "next_state"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["stateful_id", "stateful_type"], name: "index_spree_state_changes_on_stateful_id_and_stateful_type"
  end

  create_table "spree_states", force: :cascade do |t|
    t.string "name"
    t.string "abbr"
    t.bigint "country_id"
    t.datetime "updated_at"
    t.datetime "created_at"
    t.index ["country_id"], name: "index_spree_states_on_country_id"
  end

  create_table "spree_stock_item_units", force: :cascade do |t|
    t.string "number"
    t.datetime "expiry_date"
    t.string "remark"
    t.string "vendor_receipt_number"
    t.string "vendor_inventory_number"
    t.datetime "vendor_receipt_date"
    t.boolean "printed", default: false
    t.string "state", default: "stock"
    t.bigint "stock_item_id"
    t.bigint "shipment_id"
    t.bigint "line_item_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "pack_size", default: 1
    t.string "vendor_inventory_cost"
    t.string "pack_size_counter"
    t.integer "expiry_type", default: 0
    t.index ["line_item_id"], name: "index_spree_stock_item_units_on_line_item_id"
    t.index ["shipment_id"], name: "index_spree_stock_item_units_on_shipment_id"
    t.index ["stock_item_id"], name: "index_spree_stock_item_units_on_stock_item_id"
  end

  create_table "spree_stock_items", force: :cascade do |t|
    t.bigint "stock_location_id"
    t.bigint "variant_id"
    t.integer "count_on_hand", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "backorderable", default: false
    t.datetime "deleted_at"
    t.integer "inventory_threshold"
    t.bigint "stock_location_section_id"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.index "stock_location_id, variant_id, stock_location_section_id, COALESCE(deleted_at, '1970-01-01 00:00:00'::timestamp without time zone)", name: "unique_stock_item", unique: true
    t.index ["backorderable"], name: "index_spree_stock_items_on_backorderable"
    t.index ["deleted_at"], name: "index_spree_stock_items_on_deleted_at"
    t.index ["stock_location_id", "variant_id", "stock_location_section_id"], name: "stock_item_by_loc_variant_section_id"
    t.index ["stock_location_id", "variant_id"], name: "stock_item_by_loc_and_var_id"
    t.index ["stock_location_id"], name: "index_spree_stock_items_on_stock_location_id"
    t.index ["stock_location_section_id"], name: "index_spree_stock_items_on_stock_location_section_id"
    t.index ["variant_id"], name: "index_spree_stock_items_on_variant_id"
  end

  create_table "spree_stock_locations", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "default", default: false, null: false
    t.string "address1"
    t.string "address2"
    t.string "city"
    t.bigint "state_id"
    t.string "state_name"
    t.bigint "country_id"
    t.string "zipcode"
    t.string "phone"
    t.boolean "active", default: true
    t.boolean "backorderable_default", default: false
    t.boolean "propagate_all_variants", default: true
    t.string "admin_name"
    t.string "time_zone", default: "UTC"
    t.bigint "store_id"
    t.integer "channel"
    t.jsonb "ebay"
    t.string "company_name"
    t.index ["active"], name: "index_spree_stock_locations_on_active"
    t.index ["backorderable_default"], name: "index_spree_stock_locations_on_backorderable_default"
    t.index ["country_id"], name: "index_spree_stock_locations_on_country_id"
    t.index ["propagate_all_variants"], name: "index_spree_stock_locations_on_propagate_all_variants"
    t.index ["state_id"], name: "index_spree_stock_locations_on_state_id"
    t.index ["store_id"], name: "index_spree_stock_locations_on_store_id"
  end

  create_table "spree_stock_movements", force: :cascade do |t|
    t.bigint "stock_item_id"
    t.integer "quantity", default: 0
    t.string "action"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "originator_type"
    t.bigint "originator_id"
    t.index ["originator_id", "originator_type"], name: "index_stock_movements_on_originator_id_and_originator_type"
    t.index ["stock_item_id"], name: "index_spree_stock_movements_on_stock_item_id"
  end

  create_table "spree_stock_transfers", force: :cascade do |t|
    t.string "type"
    t.string "reference"
    t.bigint "source_location_id"
    t.bigint "destination_location_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "number"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.bigint "store_id"
    t.index ["destination_location_id"], name: "index_spree_stock_transfers_on_destination_location_id"
    t.index ["number"], name: "index_spree_stock_transfers_on_number", unique: true
    t.index ["source_location_id"], name: "index_spree_stock_transfers_on_source_location_id"
    t.index ["store_id"], name: "index_spree_stock_transfers_on_store_id"
  end

  create_table "spree_store_credit_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "spree_store_credit_events", force: :cascade do |t|
    t.bigint "store_credit_id", null: false
    t.string "action", null: false
    t.decimal "amount", precision: 8, scale: 2
    t.string "authorization_code", null: false
    t.decimal "user_total_amount", precision: 8, scale: 2, default: "0.0", null: false
    t.bigint "originator_id"
    t.string "originator_type"
    t.datetime "deleted_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["originator_id", "originator_type"], name: "spree_store_credit_events_originator"
    t.index ["store_credit_id"], name: "index_spree_store_credit_events_on_store_credit_id"
  end

  create_table "spree_store_credit_types", force: :cascade do |t|
    t.string "name"
    t.integer "priority"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["priority"], name: "index_spree_store_credit_types_on_priority"
  end

  create_table "spree_store_credits", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "category_id"
    t.bigint "created_by_id"
    t.decimal "amount", precision: 8, scale: 2, default: "0.0", null: false
    t.decimal "amount_used", precision: 8, scale: 2, default: "0.0", null: false
    t.text "memo"
    t.datetime "deleted_at"
    t.string "currency"
    t.decimal "amount_authorized", precision: 8, scale: 2, default: "0.0", null: false
    t.bigint "originator_id"
    t.string "originator_type"
    t.bigint "type_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "store_id"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.index ["deleted_at"], name: "index_spree_store_credits_on_deleted_at"
    t.index ["originator_id", "originator_type"], name: "spree_store_credits_originator"
    t.index ["store_id"], name: "index_spree_store_credits_on_store_id"
    t.index ["type_id"], name: "index_spree_store_credits_on_type_id"
    t.index ["user_id"], name: "index_spree_store_credits_on_user_id"
  end

  create_table "spree_store_translations", force: :cascade do |t|
    t.string "name"
    t.text "meta_description"
    t.text "meta_keywords"
    t.string "seo_title"
    t.string "facebook"
    t.string "twitter"
    t.string "instagram"
    t.string "customer_support_email"
    t.text "description"
    t.text "address"
    t.string "contact_phone"
    t.string "new_order_notifications_email"
    t.string "locale", null: false
    t.bigint "spree_store_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "deleted_at"
    t.index ["deleted_at"], name: "index_spree_store_translations_on_deleted_at"
    t.index ["locale"], name: "index_spree_store_translations_on_locale"
    t.index ["spree_store_id", "locale"], name: "index_spree_store_translations_on_spree_store_id_locale", unique: true
  end

  create_table "spree_store_users", force: :cascade do |t|
    t.bigint "store_id"
    t.bigint "user_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["store_id"], name: "index_spree_store_users_on_store_id"
    t.index ["user_id"], name: "index_spree_store_users_on_user_id"
  end

  create_table "spree_stores", force: :cascade do |t|
    t.string "name"
    t.string "url"
    t.text "meta_description"
    t.text "meta_keywords"
    t.string "seo_title"
    t.string "mail_from_address"
    t.string "default_currency"
    t.string "code"
    t.boolean "default", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "supported_currencies"
    t.string "facebook"
    t.string "twitter"
    t.string "instagram"
    t.string "default_locale"
    t.string "customer_support_email"
    t.bigint "default_country_id"
    t.text "description"
    t.text "address"
    t.string "contact_phone"
    t.string "new_order_notifications_email"
    t.bigint "checkout_zone_id"
    t.string "seo_robots"
    t.string "supported_locales"
    t.datetime "deleted_at"
    t.jsonb "settings"
    t.boolean "published", default: false
    t.string "upcitemdb_api_key"
    t.string "about_us"
    t.string "term_policies"
    t.string "contact_us"
    t.string "guarantee"
    t.string "barcodelookup_api_key"
    t.boolean "number_tracking", default: false
    t.jsonb "meta_tags", default: [], null: false
    t.string "return_refund_policy"
    t.string "shipping_delivery"
    t.boolean "risky_listing", default: false
    t.jsonb "destination_id", default: {"sqs"=>"", "event_bridge"=>""}
    t.string "price_analytics_url"
    t.string "price_analytics_client_id"
    t.string "price_analytics_client_secret"
    t.index ["code"], name: "index_spree_stores_on_code", unique: true
    t.index ["default"], name: "index_spree_stores_on_default"
    t.index ["deleted_at"], name: "index_spree_stores_on_deleted_at"
    t.index ["url"], name: "index_spree_stores_on_url"
  end

  create_table "spree_stripe_tax_calculations", force: :cascade do |t|
    t.string "calculation_id"
    t.jsonb "data"
    t.bigint "order_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["order_id"], name: "index_spree_stripe_tax_calculations_on_order_id"
  end

  create_table "spree_stripe_tax_line_items", force: :cascade do |t|
    t.string "li_id"
    t.string "calculation_id"
    t.jsonb "data"
    t.bigint "line_item_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["line_item_id"], name: "index_spree_stripe_tax_line_items_on_line_item_id"
  end

  create_table "spree_stripe_tax_settings", force: :cascade do |t|
    t.boolean "stripe_tax_enabled", default: false
    t.string "stripe_client_secret"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "store_id"
    t.index ["store_id"], name: "index_spree_stripe_tax_settings_on_store_id"
  end

  create_table "spree_stripe_tax_transactions", force: :cascade do |t|
    t.string "transaction_id"
    t.string "action_type"
    t.string "state"
    t.jsonb "data"
    t.bigint "order_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["order_id"], name: "index_spree_stripe_tax_transactions_on_order_id"
  end

  create_table "spree_subscriber_behaviors", force: :cascade do |t|
    t.bigint "subscriber_id"
    t.string "behavior"
    t.string "detail"
    t.string "record_type"
    t.bigint "record_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["subscriber_id"], name: "index_spree_subscriber_behaviors_on_subscriber_id"
  end

  create_table "spree_subscribers", force: :cascade do |t|
    t.integer "level", default: 0, null: false
    t.string "email", null: false
    t.string "source", default: "storefront", null: false
    t.integer "visit_count", default: 0, null: false
    t.integer "login_count", default: 0, null: false
    t.integer "order_count", default: 0, null: false
    t.integer "quantity_count", default: 0, null: false
    t.decimal "amount_sum", precision: 10, scale: 2, default: "0.0", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "name"
    t.integer "sale_channel_id"
    t.boolean "campaign_subscribed", default: true
    t.index ["email"], name: "index_spree_subscribers_on_email", unique: true
  end

  create_table "spree_subscriptions", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "line_item_id"
    t.string "interval"
    t.datetime "next_delivery_date"
    t.integer "status"
    t.integer "quantity"
    t.decimal "initial_price"
    t.decimal "recurring_price"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "is_active", default: true
    t.index ["line_item_id"], name: "index_spree_subscriptions_on_line_item_id"
    t.index ["user_id"], name: "index_spree_subscriptions_on_user_id"
  end

  create_table "spree_tags", force: :cascade do |t|
    t.string "name"
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "description"
    t.index ["store_id"], name: "index_spree_tags_on_store_id"
  end

  create_table "spree_tax_categories", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.boolean "is_default", default: false
    t.datetime "deleted_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "tax_code"
    t.bigint "store_id"
    t.index ["deleted_at"], name: "index_spree_tax_categories_on_deleted_at"
    t.index ["is_default"], name: "index_spree_tax_categories_on_is_default"
    t.index ["store_id"], name: "index_spree_tax_categories_on_store_id"
  end

  create_table "spree_tax_rates", force: :cascade do |t|
    t.decimal "amount", precision: 8, scale: 5
    t.bigint "zone_id"
    t.bigint "tax_category_id"
    t.boolean "included_in_price", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "name"
    t.boolean "show_rate_in_label", default: true
    t.datetime "deleted_at"
    t.bigint "store_id"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.index ["deleted_at"], name: "index_spree_tax_rates_on_deleted_at"
    t.index ["included_in_price"], name: "index_spree_tax_rates_on_included_in_price"
    t.index ["show_rate_in_label"], name: "index_spree_tax_rates_on_show_rate_in_label"
    t.index ["store_id"], name: "index_spree_tax_rates_on_store_id"
    t.index ["tax_category_id"], name: "index_spree_tax_rates_on_tax_category_id"
    t.index ["zone_id"], name: "index_spree_tax_rates_on_zone_id"
  end

  create_table "spree_taxjar_settings", force: :cascade do |t|
    t.string "taxjar_api_key"
    t.boolean "taxjar_enabled", default: false
    t.boolean "taxjar_debug_enabled", default: false
    t.boolean "taxjar_sandbox_environment_enabled", default: false
    t.bigint "store_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["store_id"], name: "index_spree_taxjar_settings_on_store_id"
  end

  create_table "spree_taxon_translations", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.string "locale", null: false
    t.bigint "spree_taxon_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "meta_title"
    t.string "meta_description"
    t.string "meta_keywords"
    t.string "permalink"
    t.index ["locale", "permalink"], name: "permalink_per_locale_idx"
    t.index ["locale"], name: "index_spree_taxon_translations_on_locale"
    t.index ["spree_taxon_id", "locale"], name: "index_spree_taxon_translations_on_spree_taxon_id_and_locale", unique: true
  end

  create_table "spree_taxonomies", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "position", default: 0
    t.bigint "store_id"
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.index ["name", "store_id"], name: "index_spree_taxonomies_on_name_and_store_id", unique: true
    t.index ["position"], name: "index_spree_taxonomies_on_position"
    t.index ["store_id"], name: "index_spree_taxonomies_on_store_id"
  end

  create_table "spree_taxonomy_translations", force: :cascade do |t|
    t.string "name"
    t.string "locale", null: false
    t.bigint "spree_taxonomy_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["locale"], name: "index_spree_taxonomy_translations_on_locale"
    t.index ["spree_taxonomy_id", "locale"], name: "index_spree_taxonomy_translations_on_spree_taxonomy_id_locale", unique: true
  end

  create_table "spree_taxons", force: :cascade do |t|
    t.bigint "parent_id"
    t.integer "position", default: 0
    t.string "name"
    t.string "permalink"
    t.bigint "taxonomy_id"
    t.bigint "lft"
    t.bigint "rgt"
    t.text "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "meta_title"
    t.string "meta_description"
    t.string "meta_keywords"
    t.integer "depth"
    t.boolean "hide_from_nav", default: false
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.string "category_id"
    t.boolean "exclude_from_data_feed", default: false
    t.index ["lft"], name: "index_spree_taxons_on_lft"
    t.index ["name", "parent_id", "taxonomy_id"], name: "index_spree_taxons_on_name_and_parent_id_and_taxonomy_id", unique: true
    t.index ["name"], name: "index_spree_taxons_on_name"
    t.index ["parent_id"], name: "index_taxons_on_parent_id"
    t.index ["permalink", "parent_id", "taxonomy_id"], name: "index_spree_taxons_on_permalink_and_parent_id_and_taxonomy_id", unique: true
    t.index ["permalink"], name: "index_taxons_on_permalink"
    t.index ["position"], name: "index_spree_taxons_on_position"
    t.index ["rgt"], name: "index_spree_taxons_on_rgt"
    t.index ["taxonomy_id"], name: "index_taxons_on_taxonomy_id"
  end

  create_table "spree_tracker_logs", force: :cascade do |t|
    t.string "trackable_type", null: false
    t.bigint "trackable_id", null: false
    t.jsonb "details"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["trackable_type", "trackable_id"], name: "index_spree_tracker_logs_on_trackable"
  end

  create_table "spree_trackers", force: :cascade do |t|
    t.string "analytics_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "engine", default: 0, null: false
    t.index ["active"], name: "index_spree_trackers_on_active"
  end

  create_table "spree_users", force: :cascade do |t|
    t.string "encrypted_password", limit: 128
    t.string "password_salt", limit: 128
    t.string "email"
    t.string "remember_token"
    t.string "persistence_token"
    t.string "reset_password_token"
    t.string "perishable_token"
    t.integer "sign_in_count", default: 0, null: false
    t.integer "failed_attempts", default: 0, null: false
    t.datetime "last_request_at"
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "login"
    t.bigint "ship_address_id"
    t.bigint "bill_address_id"
    t.string "authentication_token"
    t.string "unlock_token"
    t.datetime "locked_at"
    t.datetime "reset_password_sent_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.string "spree_api_key", limit: 48
    t.datetime "remember_created_at"
    t.datetime "deleted_at"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.boolean "axel_admin", default: false
    t.string "user_token"
    t.string "first_name"
    t.string "last_name"
    t.string "selected_locale"
    t.string "uid"
    t.integer "provider", default: 0, null: false
    t.index ["bill_address_id"], name: "index_spree_users_on_bill_address_id"
    t.index ["deleted_at"], name: "index_spree_users_on_deleted_at"
    t.index ["email"], name: "email_idx_unique", unique: true
    t.index ["ship_address_id"], name: "index_spree_users_on_ship_address_id"
    t.index ["spree_api_key"], name: "index_spree_users_on_spree_api_key"
  end

  create_table "spree_variants", force: :cascade do |t|
    t.string "sku", default: "", null: false
    t.decimal "weight", precision: 8, scale: 2, default: "0.0"
    t.decimal "height", precision: 8, scale: 2
    t.decimal "width", precision: 8, scale: 2
    t.decimal "depth", precision: 8, scale: 2
    t.datetime "deleted_at"
    t.boolean "is_master", default: false
    t.bigint "product_id"
    t.decimal "cost_price", precision: 10, scale: 2
    t.integer "position"
    t.string "cost_currency"
    t.boolean "track_inventory", default: true
    t.bigint "tax_category_id"
    t.datetime "updated_at", null: false
    t.datetime "discontinue_on"
    t.datetime "created_at", null: false
    t.jsonb "public_metadata"
    t.jsonb "private_metadata"
    t.string "upc"
    t.datetime "expiry_date"
    t.string "receipt_number"
    t.string "inventory_number"
    t.datetime "vendor_receipt_date"
    t.decimal "lbs"
    t.decimal "oz"
    t.string "barcode"
    t.index ["barcode"], name: "index_spree_variants_on_barcode"
    t.index ["deleted_at"], name: "index_spree_variants_on_deleted_at"
    t.index ["discontinue_on"], name: "index_spree_variants_on_discontinue_on"
    t.index ["is_master"], name: "index_spree_variants_on_is_master"
    t.index ["position"], name: "index_spree_variants_on_position"
    t.index ["product_id"], name: "index_spree_variants_on_product_id"
    t.index ["sku"], name: "index_spree_variants_on_sku"
    t.index ["tax_category_id"], name: "index_spree_variants_on_tax_category_id"
    t.index ["track_inventory"], name: "index_spree_variants_on_track_inventory"
  end

  create_table "spree_variants_volume_price_models", id: :serial, force: :cascade do |t|
    t.integer "volume_price_model_id"
    t.integer "variant_id"
    t.index ["variant_id"], name: "variant_id"
    t.index ["volume_price_model_id"], name: "volume_price_model_id"
  end

  create_table "spree_volume_price_models", id: :serial, force: :cascade do |t|
    t.string "name"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "spree_volume_prices", id: :serial, force: :cascade do |t|
    t.bigint "variant_id"
    t.string "name"
    t.string "range"
    t.decimal "amount", precision: 8, scale: 2
    t.integer "position"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "discount_type"
    t.bigint "role_id"
    t.bigint "volume_price_model_id"
    t.integer "listing_id"
  end

  create_table "spree_walmart_categories", force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["name"], name: "index_spree_walmart_categories_on_name", unique: true
  end

  create_table "spree_walmart_item_sync_records", force: :cascade do |t|
    t.string "event_type", null: false
    t.string "sku", null: false
    t.string "notification", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "oauth_application_id"
    t.integer "store_id"
    t.index ["sku"], name: "index_spree_walmart_item_sync_records_on_sku"
  end

  create_table "spree_walmart_item_visible_object_properties", force: :cascade do |t|
    t.string "name", null: false
    t.string "hash", null: false
    t.jsonb "content", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["hash"], name: "index_spree_walmart_item_visible_object_properties_on_hash", unique: true
  end

  create_table "spree_walmart_item_visible_object_property_relationships", force: :cascade do |t|
    t.integer "walmart_item_visible_object_property_id", null: false
    t.integer "walmart_item_visible_object_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["walmart_item_visible_object_id", "walmart_item_visible_object_property_id"], name: "index_spree_walmart_item_vopr_on_obj_id_and_prop_id", unique: true
  end

  create_table "spree_walmart_item_visible_objects", force: :cascade do |t|
    t.string "feed_type", null: false
    t.jsonb "content", null: false
    t.integer "walmart_product_type_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "spree_walmart_listing_skus", force: :cascade do |t|
    t.integer "listing_id", null: false
    t.integer "sale_channel_id", null: false
    t.string "sku", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "wpid"
    t.string "variant_group_id"
    t.string "gtin"
    t.string "item_id"
    t.index ["listing_id"], name: "index_spree_walmart_listing_skus_on_listing_id"
    t.index ["sale_channel_id"], name: "index_spree_walmart_listing_skus_on_sale_channel_id"
    t.index ["sku"], name: "index_spree_walmart_listing_skus_on_sku"
  end

  create_table "spree_walmart_order_hooks", force: :cascade do |t|
    t.string "order_id"
    t.datetime "hook_received_at"
    t.string "event_type"
    t.string "error_details"
    t.json "payload"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "spree_walmart_product_type_groups", force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.integer "walmart_category_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["walmart_category_id", "name"], name: "spree_walmart_pt_groups_on_category_id_and_name", unique: true
  end

  create_table "spree_walmart_product_types", force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.integer "walmart_product_type_group_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["name"], name: "index_spree_walmart_product_types_on_name", unique: true
  end

  create_table "spree_walmart_publish_tasks", force: :cascade do |t|
    t.string "sku", null: false
    t.integer "listing_id", null: false
    t.string "feed_type", null: false
    t.jsonb "content", null: false
    t.integer "status", default: 0, null: false
    t.string "feed_id"
    t.integer "oauth_application_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "spree_walmart_reconciliations", force: :cascade do |t|
    t.integer "oauth_application_id", null: false
    t.datetime "report_date", null: false
    t.string "purchase_order", null: false
    t.string "transaction_type", null: false
    t.string "transaction_description", null: false
    t.string "amount_type", null: false
    t.float "amount", null: false
    t.float "commission_rate", null: false
    t.jsonb "content", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["purchase_order", "amount_type"], name: "index_spree_walmart_recon_on_purchase_order_and_amount_type"
    t.index ["purchase_order", "transaction_description"], name: "index_spree_walmart_recon_on_purchase_order_and_tx_desc"
    t.index ["purchase_order"], name: "index_spree_walmart_reconciliations_on_purchase_order"
  end

  create_table "spree_walmart_report_contents", force: :cascade do |t|
    t.integer "oauth_application_id", null: false
    t.string "sku", null: false
    t.jsonb "content", null: false
    t.string "lifecycle_status", null: false
    t.string "publish_status", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["oauth_application_id", "sku"], name: "spree_walmart_report_contents_on_oa_id_and_name", unique: true
  end

  create_table "spree_walmart_reports", force: :cascade do |t|
    t.string "request_id", null: false
    t.string "report_type", null: false
    t.integer "status", default: 0, null: false
    t.datetime "request_submission_date"
    t.datetime "report_generation_date"
    t.integer "oauth_application_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["request_id"], name: "index_spree_walmart_reports_on_request_id", unique: true
  end

  create_table "spree_walmart_set_cookies", force: :cascade do |t|
    t.jsonb "content", null: false
    t.integer "oauth_application_id", null: false
  end

  create_table "spree_webhooks_events", force: :cascade do |t|
    t.integer "execution_time"
    t.string "name", null: false
    t.string "request_errors"
    t.string "response_code"
    t.bigint "subscriber_id", null: false
    t.boolean "success"
    t.string "url", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["response_code"], name: "index_spree_webhooks_events_on_response_code"
    t.index ["subscriber_id"], name: "index_spree_webhooks_events_on_subscriber_id"
    t.index ["success"], name: "index_spree_webhooks_events_on_success"
  end

  create_table "spree_webhooks_subscribers", force: :cascade do |t|
    t.string "url", null: false
    t.boolean "active", default: false
    t.jsonb "subscriptions"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "secret_key", null: false
    t.index ["active"], name: "index_spree_webhooks_subscribers_on_active"
  end

  create_table "spree_wished_items", force: :cascade do |t|
    t.bigint "variant_id"
    t.bigint "wishlist_id"
    t.integer "quantity", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "listing_id"
    t.index ["variant_id", "wishlist_id", "listing_id"], name: "index_spree_wished_items_on_variant_wishlist_listing"
    t.index ["variant_id"], name: "index_spree_wished_items_on_variant_id"
    t.index ["wishlist_id"], name: "index_spree_wished_items_on_wishlist_id"
  end

  create_table "spree_wishlists", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "store_id"
    t.string "name"
    t.string "token", null: false
    t.boolean "is_private", default: true, null: false
    t.boolean "is_default", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["store_id"], name: "index_spree_wishlists_on_store_id"
    t.index ["token"], name: "index_spree_wishlists_on_token", unique: true
    t.index ["user_id", "is_default"], name: "index_spree_wishlists_on_user_id_and_is_default"
    t.index ["user_id"], name: "index_spree_wishlists_on_user_id"
  end

  create_table "spree_zone_members", force: :cascade do |t|
    t.string "zoneable_type"
    t.bigint "zoneable_id"
    t.bigint "zone_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["zone_id"], name: "index_spree_zone_members_on_zone_id"
    t.index ["zoneable_id", "zoneable_type"], name: "index_spree_zone_members_on_zoneable_id_and_zoneable_type"
  end

  create_table "spree_zones", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.boolean "default_tax", default: false
    t.integer "zone_members_count", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "kind", default: "state"
    t.index ["default_tax"], name: "index_spree_zones_on_default_tax"
    t.index ["kind"], name: "index_spree_zones_on_kind"
  end

  create_table "stock_location_sections", force: :cascade do |t|
    t.bigint "stock_location_id", null: false
    t.string "name", null: false
    t.string "description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["stock_location_id", "name"], name: "index_stock_location_sections_on_stock_location_id_and_name", unique: true
    t.index ["stock_location_id"], name: "index_stock_location_sections_on_stock_location_id"
  end

  create_table "taggings", force: :cascade do |t|
    t.bigint "tag_id"
    t.string "taggable_type"
    t.bigint "taggable_id"
    t.string "tagger_type"
    t.bigint "tagger_id"
    t.string "context", limit: 128
    t.datetime "created_at"
    t.string "tenant", limit: 128
    t.index ["context"], name: "index_taggings_on_context"
    t.index ["tag_id", "taggable_id", "taggable_type", "context", "tagger_id", "tagger_type"], name: "taggings_idx", unique: true
    t.index ["tag_id"], name: "index_taggings_on_tag_id"
    t.index ["taggable_id", "taggable_type", "context"], name: "taggings_taggable_context_idx"
    t.index ["taggable_id", "taggable_type", "tagger_id", "context"], name: "taggings_idy"
    t.index ["taggable_id"], name: "index_taggings_on_taggable_id"
    t.index ["taggable_type", "taggable_id"], name: "index_taggings_on_taggable_type_and_taggable_id"
    t.index ["taggable_type"], name: "index_taggings_on_taggable_type"
    t.index ["tagger_id", "tagger_type"], name: "index_taggings_on_tagger_id_and_tagger_type"
    t.index ["tagger_id"], name: "index_taggings_on_tagger_id"
    t.index ["tagger_type", "tagger_id"], name: "index_taggings_on_tagger_type_and_tagger_id"
    t.index ["tenant"], name: "index_taggings_on_tenant"
  end

  create_table "tags", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "taggings_count", default: 0
    t.index ["name"], name: "index_tags_on_name", unique: true
  end

  create_table "versions", force: :cascade do |t|
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.text "object"
    t.datetime "created_at"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "spree_oauth_access_grants", "spree_oauth_applications", column: "application_id"
  add_foreign_key "spree_oauth_access_tokens", "spree_oauth_applications", column: "application_id"
  add_foreign_key "spree_option_type_translations", "spree_option_types"
  add_foreign_key "spree_option_value_translations", "spree_option_values"
  add_foreign_key "spree_order_packages", "spree_easypost_settings", column: "easypost_setting_id"
  add_foreign_key "spree_payment_sources", "spree_payment_methods", column: "payment_method_id"
  add_foreign_key "spree_payment_sources", "spree_users", column: "user_id"
  add_foreign_key "spree_permissions", "spree_roles", column: "role_id"
  add_foreign_key "spree_product_notifications", "spree_users", column: "user_id"
  add_foreign_key "spree_product_notifications", "spree_variants", column: "variant_id"
  add_foreign_key "spree_product_property_translations", "spree_product_properties"
  add_foreign_key "spree_product_translations", "spree_products"
  add_foreign_key "spree_property_translations", "spree_properties"
  add_foreign_key "spree_stock_items", "stock_location_sections"
  add_foreign_key "spree_store_translations", "spree_stores"
  add_foreign_key "spree_taxon_translations", "spree_taxons"
  add_foreign_key "spree_taxonomy_translations", "spree_taxonomies"
  add_foreign_key "stock_location_sections", "spree_stock_locations", column: "stock_location_id"
  add_foreign_key "taggings", "tags"
end
