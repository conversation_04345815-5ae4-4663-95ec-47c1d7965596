# frozen_string_literal: true

require 'rails_helper'

RSpec.describe(Spree::Listing, type: :model) do
  let(:listing) { create(:listing) }

  describe 'enums' do
    it {
      expect(listing).to(define_enum_for(:status).with_values(
        draft: 0,
        Active: 1,
        Ended: 2,
        Completed: 3,
        Inactive: 4,
        Incomplete: 5,
        Accepted: 6,
        InProgress: 7,
        Unlinked: 8
      ))
    }
  end

  describe 'associations' do
    it { is_expected.to(belong_to(:product).optional) }
    it { is_expected.to(belong_to(:store).class_name('Spree::Store').required) }
    it { is_expected.to(belong_to(:sale_channel).class_name('Spree::SaleChannel').required) }
    it { is_expected.to(have_many_attached(:image_files)) }
    it { is_expected.to(have_many(:listing_inventories).dependent(:destroy)) }
  end

  describe 'ransacker' do
    it 'formats status correctly' do
      # Setup: Create listings with different statuses
      listing_one = create(:listing, status: :Active)
      listing_second = create(:listing, status: :Ended)
      listing_third = create(:listing, status: :draft)
      listing_forth = create(:listing, status: :Completed)

      # Exercise: Use the ransacker to search for listings with formatted statuses
      result_active = described_class.ransack(status_eq: 'Active').result
      result_ended = described_class.ransack(status_eq: 'Ended').result
      result_draft = described_class.ransack(status_eq: 'draft').result
      result_completed = described_class.ransack(status_eq: 'Completed').result

      # Verify: Check if the listings are correctly filtered based on the formatted status
      expect(result_active).to(include(listing_one))
      expect(result_active).not_to(include(listing_second, listing_third, listing_forth))

      expect(result_ended).to(include(listing_second))
      expect(result_ended).not_to(include(listing_one, listing_third, listing_forth))

      expect(result_draft).to(include(listing_third))
      expect(result_draft).not_to(include(listing_one, listing_second, listing_forth))

      expect(result_completed).to(include(listing_forth))
      expect(result_completed).not_to(include(listing_one, listing_second, listing_third))
    end
  end

  describe '#set_custom_item_specific' do
    it 'returns formatted string for custom item specifics' do
      listing = build(:listing, ebay_item_specifics: {'Custom_Color' => 'Black', 'Custom_Size' => 'Small'})
      expect(listing.set_custom_item_specific).to(eq('Color: Black; Size: Small'))
    end

    it 'returns nil if there are no custom item specifics' do
      listing = build(:listing, ebay_item_specifics: nil)
      expect(listing.set_custom_item_specific).to(be_nil)
    end
  end

  describe '#total_listed_qty' do
    it 'calculates the total listed quantity from listing inventories' do
      create(:listing_inventory, listing: listing, total_listed_qty: 20)
      create(:listing_inventory, listing: listing, total_listed_qty: 5)
      expect(listing.total_listed_qty).to(eq(25))
    end
  end

  describe '#total_sold_qty' do
    it 'calculates the total sold quantity from listing inventories' do
      create(:listing_inventory, listing: listing, total_sold_qty: 12)
      create(:listing_inventory, listing: listing, total_sold_qty: 11)
      expect(listing.total_sold_qty).to(eq(23))
    end
  end

  describe '#total_available_qty' do
    it 'calculates the total available quantity from listing inventories' do
      create(:listing_inventory, listing: listing, total_listed_qty: 25, total_sold_qty: 20)
      create(:listing_inventory, listing: listing, total_listed_qty: 50, total_sold_qty: 20)
      expect(listing.total_available_qty).to(eq(35))
    end
  end

  it 'has a valid factory' do
    listing = build(:listing)

    expect(listing).to(be_valid)
  end
end

# == Schema Information
#
# Table name: spree_listings
#
#  id                       :bigint           not null, primary key
#  allow_offer              :boolean          default(FALSE)
#  auction_pricing          :jsonb
#  autoaccept_offer_price   :decimal(10, 2)
#  category_name            :string
#  currency                 :string
#  description              :text
#  ebay_item_specifics      :jsonb
#  end_time                 :date
#  image_classifier         :jsonb
#  item_url                 :string
#  minimum_offer_price      :decimal(10, 2)
#  offer_made_count         :integer          default(0)
#  pack_size_toggle         :boolean          default(FALSE)
#  pack_size_value          :integer          default(1)
#  quantity                 :string
#  sale_channel_hash        :json
#  sale_channel_metadata    :jsonb
#  sku                      :string
#  start_time               :date
#  status                   :integer
#  subscription_details     :jsonb
#  title                    :string
#  uploaded_by              :string
#  variant_stock_items_data :jsonb
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  category_id              :string
#  condition_id             :string
#  item_id                  :string
#  payment_id               :string
#  product_id               :bigint           indexed
#  return_id                :string
#  sale_channel_id          :bigint           indexed
#  shipping_category_id     :bigint
#  shipping_id              :string
#  stock_item_id            :string
#  store_id                 :bigint           indexed
#
# Indexes
#
#  index_spree_listings_on_product_id       (product_id)
#  index_spree_listings_on_sale_channel_id  (sale_channel_id)
#  index_spree_listings_on_store_id         (store_id)
#
