module Spree
  class Listing < ApplicationRecord
    include Spree::TranslatableResourceScopes
    include Spree::Listings
    # Define condition as a virtual attribute if needed
    attr_accessor :condition, :item_specifics, :selected_remove_images, :taxon, :custom_item_specifics, :price_0, :min_vic, :avg_vic, :max_vic, :upc, :fullfillment, :fullfillment_id, :payment, :return, :variants, :listing_auction_pricing
    enum status: { draft: 0, Active: 1, Ended: 2, Completed: 3, Inactive: 4, Incomplete: 5, Accepted: 6, InProgress: 7, Unlinked: 8 }, _default: :draft

    belongs_to :shipping_category, class_name: 'Spree::ShippingCategory', optional: true
    belongs_to :product, optional: true

    belongs_to :store, class_name: 'Spree::Store', optional: false
    belongs_to :sale_channel, class_name: 'Spree::SaleChannel', optional: false
    has_many :image_uploads, as: :imageable, dependent: :destroy
    has_many :volume_prices, -> { where(volume_price_model_id: nil).order(position: :asc) }, dependent: :destroy
    has_many :frequently_boughts, dependent: :destroy
    has_many :featured_products, class_name: "Spree::FeaturedProduct"
    has_many :activity_logs, as: :loggable, class_name: 'Spree::ActivityLog'

    validates :title, :currency, presence: true

    accepts_nested_attributes_for :volume_prices, allow_destroy: true,
      reject_if: proc { |volume_price|
        volume_price[:amount].blank? && volume_price[:range].blank?
      }

    if Rails.env.test?
      has_many_attached :image_files, service: :test
    else
      has_many_attached :image_files, service: :amazon
    end
    has_many :listing_inventories, dependent: :destroy

    accepts_nested_attributes_for :listing_inventories

    has_many :listing_variants, through: :listing_inventories, source: :variant
    has_many :listing_selected_variants, -> { where(spree_listing_inventories: { is_selected: true }) }, through: :listing_inventories, source: :variant
    has_many :product_variants, through: :product, source: :variant

    # rubocop:disable Rails/HasManyOrHasOneDependent
    has_many :line_items, class_name: 'Spree::LineItem'
    # rubocop:enable Rails/HasManyOrHasOneDependent

    has_many :subscriptions, through: :line_items, class_name: 'Spree::Subscription'

    has_many :walmart_publish_tasks, class_name: 'Spree::WalmartPublishTask', dependent: :destroy
    has_many :walmart_listing_skus, class_name: 'Spree::WalmartListingSku', dependent: :destroy

    before_save :update_pack_size_value, unless: -> { pack_size_toggle }
    before_save :update_shipping_category_id, if: -> { shipping_category.nil? }

    before_save :update_product_and_listing_in_elasticsearch_with_check
    before_update :update_product_and_listing_in_elasticsearch_with_check
    before_destroy :update_product_in_elasticsearch
    after_destroy :delete_listing_in_elasticsearch
    after_create :update_product_and_listing_in_elasticsearch
    after_touch(:update_product_and_listing_in_elasticsearch)
    after_save :update_walmart_listing_skus

    scope :for_store, ->(store) { where(store_id: store.id) }
    scope :active, -> { where(status: "Active") }
    scope :storefront, -> {active.joins(:sale_channel).where(spree_sale_channels: { brand: "storefront" })}
    scope :walmart, -> {active.joins(:sale_channel).where(spree_sale_channels: { brand: "Walmart" })}
    scope :amazon, -> {active.joins(:sale_channel).where(spree_sale_channels: { brand: "amazon" })}
    scope :ebay, -> {active.joins(:sale_channel).where(spree_sale_channels: { brand: "eBay" })}
    scope :in_stock, -> {active.joins(listing_selected_variants: :stock_items).where("#{Spree::StockItem.table_name}.count_on_hand > ? OR #{Spree::Variant.table_name}.track_inventory = ?", 0, false) }
    scope :backorderable, -> { active.joins(listing_selected_variants: :stock_items).where(spree_stock_items: { backorderable: true }) }
    scope :in_stock_or_backorderable, -> { in_stock.or(backorderable) }
    scope :in_stock_or_backorderable, -> { joins.or(backorderable) }

    scope :with_option_value, ->(option, value) {
      option_type_id = case option
                       when OptionType then option.id
                       when Integer then option
                       else
                         if OptionType.column_for_attribute('id').type == :uuid
                           OptionType.where(id: option).or(OptionType.where(name: option))&.first&.id
                         else
                           OptionType.where(name: option)&.first&.id
                           OptionType.where(name: option)&.first&.id
                         end
                       end

      return Product.group("#{Spree::Product.table_name}.id").none if option_type_id.blank?

      group("#{Spree::Listing.table_name}.id").
        joins(product: { variants_including_master: :option_values }).
        joins(:listing_selected_variants).
        join_translation_table(Spree::OptionValue).
        where(Spree::OptionValue.translation_table_alias => { name: value },
              Spree::OptionValue.table_name => { option_type_id: option_type_id })
    }

    scope :with_property_values, ->(property_filter_param, property_values) {
      joins(product: {product_properties: :property}).
      joins(:listing_selected_variants).
      join_translation_table(Property).
      join_translation_table(ProductProperty).
      where(Property.translation_table_alias => { filter_param: property_filter_param }).
      where(ProductProperty.translation_table_alias => { filter_param: property_values.map(&:parameterize) })
    }

    ransacker :status, formatter: proc { |it| statuses[it] }

    def update_pack_size_value
      self.pack_size_value = 1
    end

    def update_shipping_category_id
      shipping_category = Spree::ShippingCategory.find_or_create_by(name: 'Free Shipping', store_id: store.id)

      self.shipping_category_id = shipping_category.id
    end

    def set_custom_item_specific
      custom_pairs = ebay_item_specifics&.select { |key, _| key.start_with?("Custom_") }
      formatted_string = custom_pairs&.map { |key, value| "#{key.to_s.sub(/^Custom_/, "")}: #{value}" }&.join("; ")
      formatted_string || nil
    end

    def listing_name
      title
    end

    def generate_url(store)
      variant_ids = product.variants.any? ? product.variants.ids : [product.master.id]
      variants_selected_ids = listing_inventories&.select{|inv| inv.is_selected == true }&.pluck(:variant_id)
      comman_variants_ids = variant_ids & variants_selected_ids
      variant_id = comman_variants_ids&.sample || product&.master&.id
      [
        "#{store.formatted_url}/#{::Spree::Config[:storefront_products_path]}",
        "/#{variant_id}/#{id}/#{product.slug}",
      ].join("")
    end

    def storefront_sale_channel?
      sale_channel&.brand == "storefront"
    end

    def variant_price(variant_id)
      listing_inventories.present? ? listing_inventories.find_by(variant_id: variant_id)&.price&.to_f : nil
    end

    def variant_compare_to_price(variant_id)
      listing_inventories.present? ? listing_inventories.find_by(variant_id: variant_id)&.compare_to_price&.to_f : nil
    end

    def variant_regular_price(variant_id)
      listing_inventories.present? ? listing_inventories.find_by(variant_id: variant_id)&.compare_at_price&.to_f : nil
    end

    def variant_is_selected(variant_id)
      listing_inventories.present? ? listing_inventories.find_by(variant_id: variant_id)&.is_selected : false
    end

    def total_listed_qty
      listing_inventories.sum(:total_listed_qty)
    end

    def total_available_qty
      listing_inventories.sum(:total_available_qty)
    end

    def total_sold_qty
      listing_inventories.sum(:total_sold_qty)
    end

    def update_product(product)
      update(product: product, title: product&.name, description: product&.description)
    end

    def update_product_in_elasticsearch
      Spree::ElasticsearchSyncRecord.find_or_create(product)
    end

    def update_listing_in_elasticsearch
      Spree::ElasticsearchSyncRecord.find_or_create(self) if id.present?
    end

    def update_product_and_listing_in_elasticsearch
      update_product_in_elasticsearch
      update_listing_in_elasticsearch
    end

    def update_product_and_listing_in_elasticsearch_with_check
      update_product_and_listing_in_elasticsearch if changed?
    end

    def delete_listing_in_elasticsearch
      Spree::ElasticsearchSyncRecord.find_or_create(self, :op_delete) if id.present?
    end

    def all_walmart_tasks_success?
      walmart_publish_tasks.all? do |task|
        task.status == "Success"
      end
    end

    def walmart_sku_to_item_id_variant_group_id_hash
      return {} if sale_channel_hash.blank? || sale_channel_hash["variants"].blank?

      variants = sale_channel_hash["variants"]
      sku_hash = {}

      variants.each do |variant_id, variant|
        next if variant.blank? || variant["response_from_apis"].blank?
        sku = variant.dig("response_from_apis", "sku")
        next if sku.blank?
        sku_hash[sku] = {
          wpid: variant.dig("response_from_apis", "wpid"),
          variant_group_id: variant.dig("response_from_apis", "variantGroupId"),
          gtin: variant.dig("response_from_apis", "gtin"),
        }
      end
      sku_hash
    end

    def oauth_application
      sale_channel&.oauth_application
    end

    def oauth_application_id
      sale_channel&.oauth_application_id
    end

    def update_walmart_listing_skus
      # if status in ["Active", "InProgress"]
      if ["Active", "InProgress"].include?(status)
        skus = walmart_sku_variant_id_hash.keys
        # Add existing skus in sale_channel_hash
        create_walmart_listing_skus(skus, walmart_sku_to_item_id_variant_group_id_hash)

        # remove invalid skus
        Spree::WalmartListingSku.where(listing_id: id).where.not(sku: skus).delete_all

        make_sure_variant_with_sku
      else
        walmart_publish_tasks.destroy_all
        walmart_listing_skus.destroy_all
      end
    end

    def create_walmart_listing_skus(skus, sku_hash = {})
      return if skus.blank?
      skus.each do |sku|
        # Find and create spree_walmart_listing_skus
        obj = Spree::WalmartListingSku.find_or_create_by(listing_id: id, sku: sku, sale_channel_id: sale_channel_id)
        wpid = sku_hash.with_indifferent_access.dig(sku.to_sym, :wpid)
        gtin = sku_hash.with_indifferent_access.dig(sku.to_sym, :gtin)
        item_id = obj.item_id
        item_id = Walmart::ItemApis::GetItemId.new(oauth_application_id).by_gtin(gtin) if gtin.present? && (item_id.blank? || obj.gtin != gtin)
        variant_group_id = sku_hash.with_indifferent_access.dig(sku.to_sym, :variant_group_id)
        if wpid.present? || variant_group_id.present?
          obj.update(wpid: wpid, variant_group_id: variant_group_id, item_id: item_id, gtin: gtin)
        end
      end
    end

    def walmart_first_shipping_node
      return nil if oauth_application_id.blank? || store_id.blank?

      apiShipNodes = Walmart::SettingApis::GetShippingShipnodes.new(store_id, oauth_application_id).from_cache || {}
      apiShipNodes.each do |node|
        return node.with_indifferent_access.dig(:shipNode)
      end
    end

    def update_variant_stock_items_data_by_variant_id(variant_id, quantity, sold_quantity)
      self.variant_stock_items_data = {} if self.variant_stock_items_data.nil?
      self.variant_stock_items_data[variant_id] = { "quantity" => quantity, "sold_quantity" => sold_quantity }
    end

    def update_walmart_item_inventory(sku, available_quantity, data_from_other_sales_channel = true)
      return nil if oauth_application_id.blank? || store_id.blank?
      shipping_node = walmart_first_shipping_node
      return if shipping_node.blank?
      variant_id_string = walmart_variant_id_by_sku(sku).to_s
      return if variant_id_string.blank?
      quantity = sale_channel_hash.dig("variants", variant_id_string, "inventory_content", "shipNodes", 0, "quantity")
      return if variant_id_string.blank?
      quantity["availToSellQty"] = available_quantity - (quantity["inputQty"] - quantity["availToSellQty"])
      quantity["inputQty"] = available_quantity

      update_variant_stock_items_data_by_variant_id(variant_id_string, available_quantity, 0)
      obj = Walmart::InventoryApis::UpdateBulkItemInventory.new(store_id, oauth_application_id)
      listing_clone = clone
      task = nil
      if data_from_other_sales_channel
        task = obj.add_as_task(listing_clone, { "sku": sku, "shipNodes": [{ "shipNode": shipping_node, "quantity": available_quantity }] }, true)
      else
        task = obj.create_as_task_without_save(
          listing_clone,
          { "sku": sku, "shipNodes": [{ "shipNode": shipping_node, "quantity": available_quantity }] }
        )
      end
      if task.class.name == "Spree::WalmartPublishTask"
        SpreeInsertion::Walmart::UpdateListingInventory.new(listing_clone, store_id).execute(task)
      end
      save
    end

    def walmart_sku_variant_id_hash
      return {} if sale_channel_hash.blank?
      variants = sale_channel_hash.dig("variants") || {}
      variants.each_with_object({}) do |(variant_id, variant), hash|
        sku = variant&.dig("item_content", "Orderable", "sku")
        hash[sku] = variant_id.to_i if sku
      end
    end

    def walmart_variant_id_sku_hash
      return {} if sale_channel_hash.blank?
      variants = sale_channel_hash.dig("variants") || {}
      variants.each_with_object({}) do |(variant_id, variant), hash|
        sku = variant&.dig("item_content", "Orderable", "sku")
        hash[variant_id.to_i] = sku if sku
      end
    end

    def walmart_variant_id_title_hash
      return {} if sale_channel_hash.blank?
      variants = sale_channel_hash.dig("variants") || {}
      variants.each_with_object({}) do |(variant_id, variant), hash|
        walmart_title = variant&.dig("response_from_apis", "productName")
        hash[variant_id.to_i] = walmart_title if walmart_title
      end
    end

    def walmart_variant_id_by_sku(sku)
      walmart_sku_variant_id_hash.dig(sku)
    end

    def walmart_sku_by_variant_id(variant_id)
      walmart_variant_id_sku_hash.dig(variant_id)
    end

    def walmart_title_by_variant_id(variant_id)
      walmart_variant_id_title_hash.dig(variant_id)
    end

    def walmart_primary_sku
      primary_variant_id = sale_channel_hash&.dig("primary_variant_id")
      primary_sku = sale_channel_hash&.dig("variants", primary_variant_id.to_s, "item_content", "Orderable", "sku")
      primary_sku
    end

    def walmart_item_in_sale_channel_hash(sku)
      return nil if oauth_application_id.blank? || store_id.blank?
      shipping_node = walmart_first_shipping_node
      return if shipping_node.blank?
      variant_id_string = walmart_variant_id_by_sku(sku).to_s
      return if variant_id_string.blank?
      sale_channel_hash.dig("variants", variant_id_string)
    end

    def walmart_item_item_content(sku)
      walmart_item = walmart_item_in_sale_channel_hash(sku)
      return nil if walmart_item.blank?
      walmart_item.dig("item_content")
    end

    def walmart_item_price_content(sku)
      walmart_item = walmart_item_in_sale_channel_hash(sku)
      return nil if walmart_item.blank?
      walmart_item.dig("price_content")
    end

    def walmart_item_inventory_content(sku)
      walmart_item = walmart_item_in_sale_channel_hash(sku)
      return nil if walmart_item.blank?
      walmart_item.dig("inventory_content")
    end

    def walmart_item_item_orderable(sku)
      walmart_item_item_content(sku)&.dig("Orderable")
    end

    def update_walmart_item_price(sku, price)
      return if price.blank?

      orderable = walmart_item_item_orderable(sku)
      orderable["price"] = price if orderable.present?

      price_content = walmart_item_price_content(sku)
      price_content["price"] = price if price_content.present?
    end

    def update_walmart_item_quantity(sku, quantity)
      inventory_content = walmart_item_inventory_content(sku)
      return if inventory_content.blank?
      return if inventory_content.dig("shipNodes").blank?
      inventory_content["shipNodes"][0]["quantity"]["inputQty"] = quantity
    end

    def update_walmart_low_availability_toggle(sku, low_availability_toggle, low_availability_value)
      walmart_item = walmart_item_in_sale_channel_hash(sku)
      return nil if walmart_item.blank?

      walmart_item["low_availability_toggle"] = low_availability_toggle
      walmart_item["low_availability_value"] = low_availability_value
    end

    def update_walmart_shipping_weight(sku, weight)
      orderable = walmart_item_item_orderable(sku)
      return if orderable.blank?
      orderable["ShippingWeight"] = weight if weight.blank?
    end

    def update_walmart_shipping_weight!(sku, weight)
      orderable = walmart_item_item_orderable(sku)
      return if orderable.blank?
      orderable["ShippingWeight"] = weight
    end

    def walmart_item_low_availability_toggle(sku)
      walmart_item = walmart_item_in_sale_channel_hash(sku)
      {
        "low_availability_toggle" => walmart_item&.dig("low_availability_toggle") || false,
        "low_availability_value" => walmart_item&.dig("low_availability_value") || 0
      }
    end

    def walmart_item_low_availability_toggle_by_variant_id(variant_id)
      walmart_item = sale_channel_hash&.dig("variants", variant_id.to_s)
      {
        "low_availability_toggle" => walmart_item&.dig("low_availability_toggle") || false,
        "low_availability_value" => walmart_item&.dig("low_availability_value") || 0
      }
    end

    def make_sure_variant_with_sku
      walmart_variant_id_sku_hash.each do |variant_id, sku|
        variant = Spree::Variant.find_by(id: variant_id)
        unless Spree::Variant.where(deleted_at: nil).where("sku ILIKE '#{sku}'").exists?
          variant.update(sku: sku) if variant.present? && variant.sku.blank?
        end
      end
    end

    def actived_frequently_boughts
      frequently_boughts.joins(:matched_listing).where(spree_listings: {status: Spree::Listing.statuses["Active"]})
    end

    def frequently_boughts_cache
      if @frequently_boughts_full.nil?
        @frequently_boughts_full = frequently_boughts.joins(:matched_listing).to_a
      end
      @frequently_boughts_full
    end

    def frequently_boughts_related_ids
      frequently_boughts_cache.pluck(:matched_listing_id)
    end

    def frequently_boughts_related?(listing_id)
      frequently_boughts_related_ids.include?(listing_id)
    end

    def matched_listings
      Spree::Listing.Active.where(id: actived_frequently_boughts.pluck(:matched_listing_id))
    end

    def selected_variants_ids
      listing_inventories&.select { |inv| inv.is_selected == true }&.map(&:variant_id)
    end

    def selected_variants
      return @selected_variants if @selected_variants.present?
      ids = product.variants.any? ? (selected_variants_ids & product.variants.ids) : (selected_variants_ids & [product.master.id])
      @selected_variants = product.variants_including_master.where(id: ids)
    end

    def default_variant
      selected_variants.ids&.include?(product.default_variant.id) ? product.default_variant : selected_variants.take
    end
  end
end

# == Schema Information
#
# Table name: spree_listings
#
#  id                       :bigint           not null, primary key
#  allow_offer              :boolean          default(FALSE)
#  auction_pricing          :jsonb
#  autoaccept_offer_price   :decimal(10, 2)
#  category_name            :string
#  currency                 :string
#  description              :text
#  ebay_item_specifics      :jsonb
#  end_time                 :date
#  image_classifier         :jsonb
#  item_url                 :string
#  minimum_offer_price      :decimal(10, 2)
#  offer_made_count         :integer          default(0)
#  pack_size_toggle         :boolean          default(FALSE)
#  pack_size_value          :integer          default(1)
#  quantity                 :string
#  sale_channel_hash        :json
#  sale_channel_metadata    :jsonb
#  sku                      :string
#  start_time               :date
#  status                   :integer
#  subscription_details     :jsonb
#  title                    :string
#  uploaded_by              :string
#  variant_stock_items_data :jsonb
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  category_id              :string
#  condition_id             :string
#  item_id                  :string
#  payment_id               :string
#  product_id               :bigint           indexed
#  return_id                :string
#  sale_channel_id          :bigint           indexed
#  shipping_category_id     :bigint
#  shipping_id              :string
#  stock_item_id            :string
#  store_id                 :bigint           indexed
#
# Indexes
#
#  index_spree_listings_on_product_id       (product_id)
#  index_spree_listings_on_sale_channel_id  (sale_channel_id)
#  index_spree_listings_on_store_id         (store_id)
#
