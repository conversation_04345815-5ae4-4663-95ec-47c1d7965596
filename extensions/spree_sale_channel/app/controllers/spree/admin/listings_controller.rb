# Required libraries
require "open-uri"
require "uri"
require 'net/http'

class ProductNotFoundError < ::StandardError
end

module Spree
  module Admin
    class ListingsController < Spree::Admin::BaseController
      include ::Spree::Admin::ElasticsearchHelper
      helper 'listing'
      helper 'storefront_listing'

      before_action :load_shipping_categories, except: [:index]
      before_action :set_product, only: %i[create update]
      before_action :set_stock_location, only: [:create]
      before_action :find_listing, only: %i[edit revise_active_listing load_item_specifics update revise_amazon_listing load_product_types update_amazon_listing get_catalog_items listing_issues revise_walmart_listing]
      before_action :check_permission, only: [:edit]
      before_action :set_permissions, only: [:index, :new, :edit]
      after_action :helper_sync_listing_to_elasticsearch, only: [:new, :create, :update, :revise_active_listing, :update_positions, :destroy, :end_item, :revise_walmart_listing]

      def base_collection
        return @base_collection if @base_collection.present?

        new_params = {
          q: {
            s: params.dig(:q, :s) || "updated_at asc",
          },
        }

        per_page = params[:per_page].presence || Spree::Backend::Config[:admin_products_per_page]

        base_search = Spree::Listing.where(store_id: current_store.id).ransack(new_params)
        @base_collection = base_search.result.order(start_time: :desc, id: :desc).page(params[:page]).per(per_page)
        @base_collection
      end

      def handle_elasticsearch_param
        with_deleted = false
        with_discontinued = false
        query_by_elasticsearch = [:title_cont, :product_name_cont].any? do |key|
          params.dig(:q, key).present?
        end
        return @listings unless query_by_elasticsearch

        base_collection

        # default is updated_at asc
        missing = "_first"
        order = AxelSpree::Elasticsearch::Search::Order.new("updated_at", 'asc', missing).to_hash
        if params.dig(:q, :s).present?
          filed, sort = params.dig(:q, :s).split(" ")
          missing = sort == "desc" ? "_first" : "_last"
          order = AxelSpree::Elasticsearch::Search::Order.new(filed, sort, missing).to_hash
        end

        with_deleted = params.dig(:q, :deleted_at_null) == "0" if params.key?(:q) && params[:q].key?(:deleted_at_null)
        with_discontinued = params.dig(:q, :not_discontinued) != "1" if params.key?(:q) && params[:q].key?(:not_discontinued)
        status = []

        status_in = params.dig(:q, :status_in)
        if status_in.present?
          status_in.each do |st|
            if st.present?
              status << st
            end
          end
        end

        sale_channel_ids = []
        sale_channel_id_in = params.dig(:q, :sale_channel_id_in)
        if sale_channel_id_in.present?
          sale_channel_id_in.each do |sc|
            if sc.present?
              sale_channel_ids << sc
            end
          end
        end

        mode = params[:mode] || "standard"

        query_params = {
          title: params.dig(:q, :title_cont),
          product_name: params.dig(:q, :product_name_cont),
          sale_channel_id: sale_channel_ids,
          start_time_between: params.dig(:q, :start_time_between),
          end_time_between: params.dig(:q, :end_time_between),
          status: status,
        }

        es_listing = AxelSpree::Elasticsearch::Listing.new
        es_listing.backend(query_params).order(order).mode(mode)
        @listings.use_elasticsearch(es_listing, @base_collection)

        @listings
      end

      def index

        # Check if session already has query parameters and set them if available
        session[:listings_query] ||= {}
        params[:q] ||= {}
        params[:q][:status_in] ||= Spree::Listing.statuses.keys - %w[draft Ended Completed Inactive Incomplete Accepted InProgress]

        if params[:q]
          permitted_params = params.require(:q).permit(:s, :title_cont, :product_name_cont, :start_time_between, :end_time_between, sale_channel_id_in: [], status_in: [])
          session[:listings_query] = session[:listings_query].merge!(permitted_params)
        end

        per_page = params[:per_page].presence || Spree::Backend::Config[:admin_products_per_page]
        @search = Spree::Listing.where(store_id: current_store.id).ransack(session[:listings_query])
        @listings = @search.result.order(start_time: :desc, id: :desc).page(params[:page]).per(per_page)
        handle_elasticsearch_param
      end

      def show
        @listing = Spree::Listing.find_by(id: params[:id])
        @product = Spree::Product.find_by(id: @listing.product_id)
        @activity_logs = @listing.activity_logs.order(created_at: :desc).page(params[:page])&.per(15)
      end

      def new

        if @permissions&.create_and_edit_listing
          #
        else
          message = "You are not authorized to do this action"
          flash[:error] = message
          redirect_to(admin_listings_path)
          return
        end
        @products = Spree::Product.find_by(id: params.to_unsafe_h.with_indifferent_access[:publish_list][:product_ids]) if params[:publish_list]
        unless @products.stock_items.any?
          flash[:error] = Spree.t('admin.listing.taxons_stock_items_not_present')
          redirect_to admin_products_path
          return
        end
        @listing = current_store.listing.create!(
                    product_id: @products.id, title: @products.name, currency: @products.currency,
                    sale_channel_id: params[:sale_channel_id] || params.dig(:publish_list, :sale_channel_id)
                  )
        if @listing.persisted?
          record_activity_log
        end
        get_general_values(@products)
        redirect_to edit_admin_listing_path(@listing)
      end

      def create

        generate_custom_item_specific(params.dig(:listing, :custom_item_specifics)) if params.dig(:listing, :custom_item_specifics).present?
        generate_variant_specific_listing_params if @products&.variants&.any? && @products.option_types.any?
        @listing = current_store.listing.new(listing_params)
        begin
          if @listing.save!
            record_activity_log
            new_params = create_listing_images(@listing, params)
            SpreeInsertion::ItemSpecific.new(new_params['item_specifics'], current_store.id).format_item_specific_record_response(@listing)
            # Run validator before API call
            pack_size = params[:pack_size_toggle].present? ? params[:pack_size].to_i : 1
            raise StandardError, Spree.t('admin.listing.quantity_validation_failed') unless validator_run_before_api_call_for_draft(@listing, pack_size)
            raise StandardError, Spree.t('admin.listing.Allow_offer_price_validation_failed') unless validator_for_allowoffer_run_before_api_call('create', @listing, params)

            get_ebay_listing(@listing, @products, new_params)
          end
        rescue StandardError => e
          flash[:error] = Spree.t('admin.listing.something_went_wrong', message: e&.message)
          redirect_to admin_listings_path
        end
      end

      def update
        previous_product_id = @listing.product_id
        @listing.update!(listing_params)
        record_activity_log
        new_params = update_listing_images(@listing, params)
        generate_custom_item_specific(new_params.dig(:listing, :custom_item_specifics)) if new_params.dig(:listing, :custom_item_specifics).present?
        begin
          SpreeInsertion::ItemSpecific.new(new_params['item_specifics'], current_store.id).format_item_specific_record_response(@listing) unless @listing.storefront_sale_channel?
          generate_variant_specific_listing_params if @products&.variants&.any? && @products.option_types.any?
          @listing = Spree::Listing.find_by(id: new_params[:id])
          update_auction_pricing(@products) if params.dig(:listing, :pricing_format) == "Auction"
          # Run validator again before API call
          pack_size = params[:pack_size_toggle].present? ? params[:pack_size]&.to_i : 1
          if @listing.storefront_sale_channel?
            raise StandardError, Spree.t("admin.storefront.listing.quantity_validation_failed", available_quantity: @listing.product.total_available) if validate_quantity_and_lot_size_for_listing(pack_size)
            raise StandardError, Spree.t("admin.storefront.listing.variant_not_selected") unless validate_variant_selected_or_not
            raise StandardError, Spree.t("admin.storefront.listing.variant_selected_quantity", message: validate_selected_variant_quantity[:message]) unless validate_selected_variant_quantity[:status]
            raise StandardError, Spree.t("admin.storefront.listing.stock_validation_failed", message: validate_stock_items.join(',\n')) if validate_stock_items.present?

            destroy_listing_inventories_on_product_change if previous_product_id != @listing.product_id
            update_listing_pricing_and_pack_size(pack_size)
            unless @listing.update(volume_pricing_attributes)
              flash[:error] = Spree.t("admin.listing.something_went_wrong", message: @listing.errors.full_messages.join("\n"))
              return redirect_to edit_admin_listing_path(@listing)
            end
            set_volume_pricing_model
          else
            raise StandardError, Spree.t('admin.listing.quantity_validation_failed') unless validator_run_before_api_call_for_draft(@listing, pack_size)
            raise StandardError, Spree.t('admin.listing.Allow_offer_price_validation_failed') unless validator_for_allowoffer_run_before_api_call('update', @listing, params)
          end
          get_ebay_listing(@listing, @products, new_params)
        rescue StandardError => e
          flash[:error] = Spree.t("admin.listing.something_went_wrong", message: e&.message)
          redirect_to edit_admin_listing_path(@listing)
        end
      end

      def revise_active_listing
        ActiveRecord::Base.transaction do
          begin
            @prev_prod_id = @listing.product.id
            @listing.image_files.attachments.destroy_all if @prev_prod_id != params['product_id'].to_i
            # raise StandardError, "Product already has been sold" if modify_variant && @listing.total_sold_qty > 0

            listing_data = update_listing_images(@listing, params)
            oauth_application = @listing.sale_channel&.oauth_application
            generate_custom_item_specific(listing_data[:custom_item_specifics]) if listing_data[:custom_item_specifics].present?
            taxon_name_value_arr = listing_data[:taxon]&.split('_')
            if params[:allow_offer].nil?
              params[:allow_offer] = 'false'
            end
            @listing.update(category_id: taxon_name_value_arr[0], category_name: taxon_name_value_arr[1]) if listing_data['taxon']
            @listing.update(shipping_id: listing_data.dig(:listing, :fullfillment_id), return_id: listing_data.dig(:listing, :return_id),
                            payment_id: listing_data.dig(:listing, :payment_id), product_id: listing_data[:product_id], image_classifier: update_image_classifier, condition_id: listing_data[:condition],
                            allow_offer: params[:allow_offer], minimum_offer_price: params[:minimum_offer_price], autoaccept_offer_price: params[:autoaccept_offer_price])
            SpreeInsertion::ItemSpecific.new(listing_data['item_specifics'], current_store.id).format_item_specific_record_response(@listing) if listing_data['item_specifics'].present?

            if listing_data[:product_id] != listing_data[:product]
              @products = Spree::Product.find_by(id: listing_data[:product])
              @listing.update_product(@products)
            else
              @products = Spree::Product.find_by(id: listing_data[:product_id])
            end
            # Run validator before api call
            pack_size = params[:pack_size_toggle].present? ? params[:pack_size] : 1
            raise StandardError, Spree.t('admin.listing.quantity_validation_failed') unless validator_run_before_api_call_for_revise(@listing)
            raise StandardError, Spree.t('admin.listing.Allow_offer_price_validation_failed') unless validator_for_allowoffer_run_before_api_call('revise', @listing, params)

            if @products&.variants&.any? && @products.option_types.any?
              listing_data[:modify_variant] = modify_variant
              listing_data[:prev_product_id] = @prev_prod_id
              generate_variant_specific_listing_params
              response = Ebay::TradingApis::ReviseFixedPriceItem.new(current_store, oauth_application.id).revise_item(@listing, listing_data)
              message = generate_response_message(response.with_indifferent_access, 'ReviseFixedPriceItemResponse')
              build_flash_message_for_revise_item(response.with_indifferent_access, 'ReviseFixedPriceItemResponse', message, @listing, listing_data,
                                                  @prev_prod_id)
            else
              response = Ebay::TradingApis::ReviseItem.new(current_store, oauth_application.id).revise_item(@listing, listing_data)
              message = generate_response_message(response.with_indifferent_access, 'ReviseItemResponse')
              build_flash_message_for_revise_item(response.with_indifferent_access, 'ReviseItemResponse', message, @listing, listing_data,
                                                  @prev_prod_id)
            end
          rescue StandardError => e
            flash[:error] = Spree.t('admin.listing.something_went_wrong', message: e&.message)
            redirect_to admin_listings_path
            raise ActiveRecord::Rollback
          end
        end
      end

      def revise_amazon_listing
        ActiveRecord::Base.transaction do
          begin
            store_id = current_store.id
            listing_data = {}
            sale_channel = Spree::SaleChannel.find_by(id: params['sale_channel_id'])
            oauth_application = sale_channel.oauth_application
            sku = params.dig('item_specifics', 'package_contains_sku-sku')
            # Validate presence of SKU
            raise StandardError, 'SKU must be present' if sku.blank?

            product_type = get_product_type(params.dig('taxon'))
            listing_data['product_type'] = product_type
            listing_data['item_specifics'] = item_specific_values
            @attributes = get_attributes_hash(sale_channel, product_type)
            response = Amazon::ListingApis::PutListingItem.new(store_id, oauth_application.id).put_listing_item(sku, listing_data, @listing)

            @listing.update!(sku: sku, category_id: params['taxon'], category_name: params['taxon']&.split("_")&.last)
            build_flash_message_for_revise_amazon_item(response, @listing)
          rescue StandardError => e
            flash[:error] = Spree.t('admin.listing.something_went_wrong', message: e&.message)
            redirect_to admin_listings_path
            raise ActiveRecord::Rollback
          end
        end
      end

      def update_amazon_listing
        ActiveRecord::Base.transaction do
          begin
            announcements = Spree::Announcement.where(subject_id: @listing.id)
            announcements.destroy_all if announcements.any? #destoy all previous announcement for that listing.
            store_id = current_store.id
            listing_data = {}
            sale_channel = Spree::SaleChannel.find_by(id: params['sale_channel_id'])
            oauth_application = sale_channel.oauth_application
            sku = params.dig('item_specifics', 'package_contains_sku-sku') || @listing.sku
            # Validate presence of SKU
            raise StandardError, 'SKU must be present' if sku.blank?

            previous_asin = @listing.sale_channel_hash.dig('attributes', 'merchant_suggested_asin')&.first&.dig('value')
            current_asin = params.dig('item_specifics', 'merchant_suggested_asin-value')
            # Destroy all previous images when the listing is being revised.
            @listing.image_files.attachments.destroy_all if previous_asin != current_asin
            product_type = get_product_type(params.dig('taxon'))
            listing_data['product_type'] = product_type
            listing_data['item_specifics'] = item_specific_values
            @attributes = get_attributes_hash(sale_channel, product_type)
            response = Amazon::ListingApis::PatchListingItem.new(store_id, oauth_application.id).patch_listing_item(sku, listing_data, @listing)

            @listing.update!(sku: sku, category_id: params['taxon'], category_name: params['taxon']&.split("_")&.last)
            build_flash_message_for_revise_amazon_item(response, @listing)
          rescue StandardError => e
            flash[:error] = Spree.t('admin.listing.something_went_wrong', message: e&.message)
            redirect_to admin_listings_path
            raise ActiveRecord::Rollback
          end
        end
      end

      def item_specific_values
        item_specific_data = {}
        params['item_specifics'].each do |prop, value|
          item_specific_data = item_specific_data.merge({prop => value})
        end
        item_specific_data = get_amazon_images(item_specific_data)
        item_specific_data
      end

      def get_amazon_images(item_specific_data)
        return item_specific_data unless params['item_specifics_images'].present?

        params['item_specifics_images'].each do |image_key, image_value|
          new_filename = "#{image_key}-#{image_value.original_filename.to_s}"
          @listing.image_files.attachments.map { |x| x.blob.filename_for_database }.each do |image_name|
            if (image_name.starts_with?(image_key))
              # delete image file of that attachment
              attachment = @listing.image_files.attachments.find { |a| a.blob.filename_for_database == image_name }
              attachment.purge if attachment
            end
          end

          if @listing.image_files.attachments.map { |x| x.blob.filename_for_database }.include?(new_filename)
            existing_image_attachment = @listing.image_files.attachments.select { |image_file| image_file.url if image_file.filename == new_filename }
            existing_image_url = existing_image_attachment.last.url
          else
            @listing.image_files.attach(io: params['item_specifics_images'][image_key], filename: new_filename, content_type: image_value.content_type)
            image_url = @listing.image_files.attachments.last.url
          end
        end
        @listing.image_files.attachments.each do |image_file|
          name = image_file.filename.to_s.split('-')
          item_specific_data[name[0...-1].join('-')] = image_file.url.split('?').first
        end
        item_specific_data
      end

      def get_product_type(taxon)
        if taxon
          taxon_parts = taxon.split('_')
          product_type = (taxon_parts - [taxon_parts.last]).join('_')
        else
          product_type = ''
        end
      end

      def update_positions
        @listing = Spree::Listing.find_by(id: params['listing_id'])
        @listing.update(image_classifier: params['positions'])
      end

      def update_image_classifier
        product = @products.presence || Spree::Product.find_by(id: params['product_id'])
        return {} unless product.option_types.present?

        if modify_variant
          build_option_type_position(product)
        else
          @listing.image_classifier.presence || build_option_type_position(product)
        end
      end

      def get_ebay_listing(listing, products, params)

        oauth_application = listing.sale_channel.oauth_application
        update_listing_with_stock_items_on_publish(listing, products)
        storefront_sale_channel = listing.sale_channel.brand == "storefront"
        if params[:commit] == 'Save to draft'
          if storefront_sale_channel
            update_subscription_details_hash(@listing)
          else
            find_or_create_listing_inventories_data_on_publish(@listing)
            update_low_availability_alert(@listing)
          end
          flash[:success] = Spree.t('admin.listing.save_to_draft')
        elsif params[:commit] == 'Update' && storefront_sale_channel
          update_item_url(@listing)
          update_subscription_details_hash(@listing)
          flash[:success] = Spree.t('admin.listing.listing_successfully_updated')
        elsif params[:commit] == 'Publish' && storefront_sale_channel
          storefront_listing_update(@listing)
          update_subscription_details_hash(@listing)
          flash[:success] = Spree.t('admin.listing.storefront_listing_successfully_created')
        elsif products&.variants&.any? && products.option_types.any?
          response = Ebay::TradingApis::AddFixedPriceItem.new(current_store, oauth_application.id).add_fixed_price_item(listing, params)
          message = generate_response_message(response.with_indifferent_access, 'AddFixedPriceItemResponse')
          build_flash_message(response.with_indifferent_access, 'AddFixedPriceItemResponse', message, listing)
        else
          response = Ebay::TradingApis::AddItem.new(current_store, oauth_application.id).add_item(listing, params)
          message = generate_response_message(response.with_indifferent_access, 'AddItemResponse')
          build_flash_message(response.with_indifferent_access, 'AddItemResponse', message, listing)
        end
        redirect_to admin_listings_path
      end

      def edit
        if @permissions&.create_and_edit_listing
          #
        else
          message = "You are not authorized to do this action"
          flash[:error] = message
          redirect_to(admin_listings_path)
          return
        end
        unless @listing
          redirect_back(fallback_location: admin_listings_path, flash: { error: 'Listing not found' })
          return
        end
        update_auction_pricing(@products) if params.dig(:listing, :starting_bid).present?

        if @listing.sale_channel.brand == 'amazon' && @listing.category_id.present?
          @attributes = get_attributes_hash(@listing.sale_channel, (@listing.category_id.split('_') -[@listing.category_id.split('_')[-1]]).join('_'))
          @attribute_values = get_attribute_value(@listing)
        end

        if @listing.is_walmart_channel?
          @walmart_inventories = @listing.walmart_inventories
        end

        @custom_item_specific_pair = @listing.set_custom_item_specific
        if params[:updated_product_id].present?
          @products = Spree::Product.find_by(id: params[:updated_product_id])
          @title = @products.name
          @description = @products.description.presence || ""
          @product_price = @products.master.price.to_f
          @product_quantity = @products.master.total_available
        else
          @products = Spree::Product.find_by(id: @listing.product_id)
        end

        if @listing.sale_channel.brand == "Walmart"
          @walmart_categories  = JSON.parse(@listing.walmart_categories.to_json)
          @walmart_product_groups = JSON.parse(@listing.walmart_product_groups.to_json)
          @walmart_product_types = JSON.parse(@listing.walmart_product_types.to_json)
          @walmart_item_specs = JSON.parse(@listing.walmart_item_spec.to_json)
          @walmart_images = get_walmart_images
        end

        raise ProductNotFoundError unless @products

        get_general_values(@products)
        rescue ProductNotFoundError => e
          redirect_back fallback_location: admin_listings_path, flash: { error: e&.message }
        rescue StandardError => e
          redirect_back fallback_location: admin_listings_path, flash: { error: e&.message }
      end

      def storefront_listing_update(listing)
        product = listing.product
        variant_ids = product.variants.any? ? product.variants.ids : [product.master.id]
        variants_selected_ids = listing.listing_inventories&.select{|inv| inv.is_selected == true }&.pluck(:variant_id)
        comman_variants_ids = variant_ids & variants_selected_ids
        variant_id = comman_variants_ids&.sample || product&.master&.id

        prefix = request.url.split("/old_admin").first
        listing.update(
          status: "Active",
          item_url: "#{prefix}/p/#{variant_id}/#{listing.id}/#{product.slug}",
          item_id: "SF" + SecureRandom.random_number(36**6).to_s(10).rjust(10, "0").upcase
        )
      end


      def update_subscription_details_hash(listing)
        return unless params.dig(:listing, :subscription_details).present?

        subscription_details = subscription_details_params
        updated_subscription_details = {}

        subscription_details.each do |key, value|
          next unless value[:subscription_toggle] == "true"

          if value[:recurring_discount_checkbox] == "true"
            updated_subscription_details[key] = value.merge(
              "recurring_discount_checkbox" => value[:recurring_discount_checkbox] == "true",
              "subscription_toggle" => value[:subscription_toggle] == "true"
            )
          else
            updated_subscription_details[key] = value.merge(
              "recurring_discount" => value[:first_time_discount],
              "first_price_per_item" => value[:first_price_per_item],
              "recurring_price_per_item" => value[:first_price_per_item]
            )
          end
        end
        previous_details = listing.subscription_details
        listing.update(subscription_details: updated_subscription_details)
        current_details = listing.subscription_details
        if (previous_details != current_details) && listing.subscriptions.any?
          end_subscription(listing, previous_details, current_details)
        end
      end

      def end_subscription(listing, previous_details, current_details)
        if current_details.empty?
          listing.subscriptions.find_each do |subscription|
            subscription.update(status: 'unsubscribe', is_active: false)
          end
        else
          previous_variant_ids = previous_details.keys.map(&:to_i)
          new_variant_ids = current_details.keys.map(&:to_i)
          removed_variant_ids = previous_variant_ids - new_variant_ids
          listing.subscriptions.find_each do |subscription|
            varaint_id = subscription.line_item.variant.id
            if removed_variant_ids.include?(varaint_id)
              subscription.update(status: 'unsubscribe', is_active: false)
            end
          end
        end
      end

      def update_item_url(listing)
        product = listing.product
        variant_ids = product.variants.any? ? product.variants.ids : [product.master.id]
        variants_selected_ids = listing.listing_inventories&.select{|inv| inv.is_selected == true }&.pluck(:variant_id)
        comman_variants_ids = variant_ids & variants_selected_ids
        variant_id = comman_variants_ids&.sample || product&.master&.id

        prefix = request.url.split("/old_admin").first
        listing.update(item_url: "#{prefix}/p/#{variant_id}/#{listing.id}/#{product.slug}")
      end

      def fetch_icon
        respond_to do |format|
          format.turbo_stream do
            render turbo_stream: [
              turbo_stream.update(
                "svg-container-#{ params[:variantId] }", # First container ID
                partial: '/spree/admin/listings/storefront/input_box_svg_icon',
                locals: { name: params[:name] }
              ),
              turbo_stream.update(
                "svg-container-currency-#{ params[:variantId] }", # Second container ID
                partial: '/spree/admin/listings/storefront/input_box_svg_icon',
                locals: { name: params[:name] }
              )
            ]
          end
        end
      end

      def get_general_values(products)
        if @listing.sale_channel.brand != "storefront"
          @fullfillment_ebay_policies = Spree::EbayPolicy.fullfillment(params[:sale_channel_id] || @listing.sale_channel_id)
          @payment_ebay_policies = Spree::EbayPolicy.payment(params[:sale_channel_id] || @listing.sale_channel_id)
          @return_ebay_policies = Spree::EbayPolicy.return(params[:sale_channel_id] || @listing.sale_channel_id)
        end

        if products.variants.any? && products.option_types.empty?
          variant_id = params[:variant_id] || @listing.variant_stock_items_data.keys[0]
          @variant = Spree::Variant.find_by(id: variant_id)
          @listing.update(variant_stock_items_data: {variant_id => {}}) unless @listing.variant_stock_items_data.present?
        elsif products.variants.any?
          @variants = products.variants
        else
          @stock_items = products.stock_items
        end
      end

      def generate_response_message(response, response_type)
        message_type = response.with_indifferent_access.dig(response_type, :Ack).to_s
        message_content = response.with_indifferent_access.dig(response_type, :Errors)
        message_content = message_content.is_a?(Array) ? message_content&.map { |x| x['LongMessage'] }&.join(',') : message_content&.dig(:LongMessage)
        "#{message_type}: #{message_content}"
      end

      def build_flash_message(response, response_type, message, listing)
        if response.dig(response_type, :Ack) == 'Failure'
          flash[:error] = Spree.t('admin.listing.something_went_wrong', message: message)
        else
          GetItemJob.perform_later(listing, current_store, response.dig(response_type, :ItemID))
          update_listing(@listing, response, response_type)
          find_or_create_listing_inventories_data_on_publish(@listing)
          update_pack_size(@listing)
          update_low_availability_alert(@listing)
          flash[:success] = Spree.t('admin.listing.listing_successfully_created', message: message)
        end
      end

      def load_policies
        @policy = Spree::EbayPolicy.find_by(id: params[:listing_id])
        @form = params[:form_object]
        render format: :js
      end

      def load_item_specifics
        sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id])
        category_id = params[:category_id]&.split('_')&.first
        @ebay_item_specifics = Ebay::TaxonomyApis::ItemSpecific.new(
          current_store.id,
          sale_channel.oauth_application.id
        ).get_item_specifics_records(category_id)
        render json: { message: 'success', data: @ebay_item_specifics, listing: @listing }
      end

      def load_product_types
        sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id])
        category = params[:category_id]&.split('_')
        category.pop()
        category_id = category.join('_')
        @attributes = get_attributes_hash(sale_channel, category_id)
        @asin = params.dig('catalog_item_payload', 'asin')
        @attribute_values = get_attribute_value(@listing, params['catalog_item_payload'])
        @attributes
        render format: :js
      end

      def get_attribute_value(listing, catalog_payload = nil)
        if catalog_payload.present?
          raw_attributes = catalog_payload.require(:attributes).permit!.to_h
          @data = transform_params(raw_attributes)
        else
          @data = listing.sale_channel_hash&.dig('attributes')
        end
        @transformed_data = flatten_hash(@data)
      end

      def transform_params(params)
        params.transform_values do |value|
          if value.is_a?(Hash)
            value.values
          else
            value
          end
        end
      end

      def flatten_hash(hash, prefix = nil)
        hash.each_with_object({}) do |(key, value), result|
          new_key = [prefix, key].compact.join('-')
          if value.is_a? Hash
            result.merge!(flatten_hash(value, new_key))
          elsif value.is_a? Array
            value.each do |item|
              next unless item.is_a?(Hash)
              result.merge!(flatten_hash(item, new_key))
            end
          else
            result[new_key] = value
          end
        end
      end

      def get_attributes_hash(sale_channel, category_id)
        @get_product_schema = Amazon::ProductTypeApis::GetDefinitionsProductType.new(current_store.id, sale_channel.oauth_application.id).get_definition(category_id.upcase)
        @get_product_schema_url = @get_product_schema.dig('schema', 'link', 'resource')
        data = URI.open(@get_product_schema_url).read
        data = JSON.parse(data).with_indifferent_access
        structured_data = Amazon::GetProductSchema.new(@get_product_schema_url).structure_data
        @listing.update(sale_channel_hash: structured_data) if params['action'] == "update_amazon_listing" ||  params['action'] == "revise_amazon_listing" || @listing.sale_channel_hash.blank?
        @attributes = {}
        data.dig('properties').each do |prop_name, prop_hash|
          intermidiate_properties = []
          items = prop_hash.dig('items')
          items.dig('properties').each do |cp_name, cp_hash|
            if cp_hash.dig('properties').present? || cp_hash.dig('items', 'properties').present?
              new_key = prop_name + "-" + cp_name
              result = process_nested_data(cp_name, (cp_hash.dig('properties') || cp_hash.dig('items', 'properties')), new_key)
              @attributes = @attributes.merge(result)
            else
              new_key = prop_name + "-" + cp_name
              @attributes = @attributes.merge({new_key => cp_hash})
            end
          end
          @attributes
        end
        unless @attributes.keys.include?('package_contains_sku-sku')
          sku_fields = { "package_contains_sku-marketplace_id"=>{"$ref"=>"#/$defs/marketplace_id"}, "package_contains_sku-quantity"=>{"title"=>"Package Contains SKU Quantity", "description"=>"Provide the quantity of each unit, case, or pallet identified in Package Level.", "editable"=>true, "hidden"=>false, "examples"=>["1"], "type"=>"integer", "minimum"=>0, "maximum"=>250}, "package_contains_sku-sku"=>{"title"=>"Package Contains SKU Identifier", "description"=>"Provide the SKU identifier of each unit, case or pallet identified in Package Level.", "editable"=>true, "hidden"=>false, "examples"=>["ABC123"], "type"=>"string", "maxLength"=>100}}
          @attributes = sku_fields.merge(@attributes)
        end
        @attributes
      end

      def process_nested_data(cp_name, data, new_key="" )
        response_data = {}
        updated_key = new_key
        data.each do |data_key, data_value|
          new_key = updated_key
          if data.dig('properties').present? || data.dig("#{data_key}", 'properties').present? || data.dig("#{data_key}", 'items', 'properties').present?
            properties = data.dig('properties') ||
              data.dig(data_key, 'properties') ||
              data.dig(data_key, 'items', 'properties')
              new_key = new_key + "-" + data_key
            response_data = process_nested_data(data_key, properties, new_key)
          else
            new_key = new_key + "-" + data_key
            response_data = response_data.merge({new_key => data_value})
          end
        end
        response_data
      end

      def get_category_features
        sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id])
        category_id = params[:category_id]&.split('_')&.first
        @condition_id = Spree::Listing.find_by(id: params[:store_listing_id])&.condition_id

        @get_category_features = Ebay::TradingApis::GetCategoryFeatures.new(
          current_store.id,
          sale_channel.oauth_application.id
        ).get_category_features(category_id)


        render json: { message: 'success', data: @get_category_features, condition_id: @condition_id }
      end

      def destroy
        @listing = Spree::Listing.find(params[:id])
        if @listing.destroy!
          record_activity_log
          flash[:success] = flash_message_for(@listing, :successfully_removed)
        else
          flash[:error] = @listing.errors.full_messages.join(', ')
        end
        respond_with(@listing) do |format|
          format.html { redirect_to admin_listings_path }
          format.js   { render_js_for_destroy }
        end
      end

      def end_item
        @listing = Spree::Listing.find_by(id: params[:listing_id])
        return if @listing.blank?

        if @listing.sale_channel&.brand == "storefront"
          @listing.update(status: 'draft', end_time: Time.now)
          flash[:success] = Spree.t('admin.listing.listing_successfully_draft')
          redirect_to admin_listings_path(listing_type: 'draft')
        elsif @listing.sale_channel&.brand.downcase == "walmart"
          walmart_end_item
        else
          response = Ebay::TradingApis::EndItem.new(current_store.id, @listing.sale_channel&.oauth_application&.id).end_item(@listing.item_id)
          message = generate_response_message(response.with_indifferent_access, 'EndItemResponse')
          build_flash_message_for_end_item(response.with_indifferent_access, 'EndItemResponse', message, @listing)
        end
      end

      def batch_end_items
        product = Spree::Product.find_by(id: params[:product_id])
        BatchEndItemsJob.perform_later(product, current_store)
        render json: { success: true }
      end

      def remove_images
        @listing = Spree::Listing.find(params[:listing_id])
        return if @listing.blank?
        result = false
        @listing.image_files.attachments.each do |img|
          next if params[:image_name] != img.filename.instance_variable_get(:@filename)

          begin
            img.purge
            result = true if @listing.image_files.attachments.map { |x| x.blob.filename_for_database }.include?(params[:image_name])
          rescue StandardError => e

            Rails.logger.error("Error purging image: #{e.message}")
            result = false
          end
        end
        result = true unless @listing.image_files.attachments.any?
        if result
          render json: { success: true, message: 'Image deleted successfully', variant_id: params['variant_id'], filename: params['image_name'] }
        else
          render json: { success: false, error: 'unable to delete' }
        end
      end

      def sync_ebay
        Spree::SaleChannel.where(brand: 'eBay').each do |channel|
          next unless channel.oauth_application

          oauth_application = channel.oauth_application
          ::SyncEbayJob.perform_later(current_store, oauth_application.id)
        end
        redirect_to admin_listings_path
      end

      def sync_amazon
        Spree::SaleChannel.where(brand: 'amazon').each do |channel|
          next unless channel.oauth_application

          oauth_application = channel.oauth_application
          ::Amazon::SyncAmazonJob.perform_now(current_store.id, oauth_application.id)
        end
        redirect_to admin_listings_path
      end

      def sync_walmart
        Spree::SaleChannel.where(brand: 'Walmart').each do |channel|
          next unless channel.oauth_application

          oauth_application = channel.oauth_application
          ::Walmart::SyncWalmartJob.perform_now(current_store.id, oauth_application.id)
        end
        redirect_to admin_listings_path
      end

      def get_taxons
        @sale_channel = Spree::SaleChannel.find_by(id: params.dig(:filter, :sale_channel_id))
        if @sale_channel.brand == 'amazon'
          taxons_list = Amazon::ProductTypeApis::SearchDefinitionsProductType.new(
            current_store,
            @sale_channel.oauth_application.id
            ).search_product_type(params.dig(:filter, :search_query))
          taxons = {}
          taxons_list.dig('productTypes').each do |type|
            type_name = type.dig("displayName")&.split('_')&.join(" ")
            taxons = taxons.merge(type.dig("name") => type_name)
          end
        else
          taxons = Ebay::TaxonomyApis::GetCategorySuggestion.new(
            current_store,
            @sale_channel.oauth_application.id
          ).get_category_suggestions(params.dig(:filter, :search_query))
        end
        render json: taxons.to_json
      end

      def upc_number_search
        sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id])
        upc = params[:upc].strip
        upc_number = upc&.length == 12 ? '0' + upc : upc
        response = Ebay::BrowseApis::Search.new(current_store.id, sale_channel.oauth_application.id).call(upc_number)
        broadcasting = [Apartment::Tenant.current, spree_current_user]

        if response.nil?
          QrCodeChannel.broadcast_to(broadcasting, type: 'show_error_message', response: 'Product not found')
        else
          QrCodeChannel.broadcast_to(broadcasting, type: 'ebay_catalog', response: response, sale_channel_id: sale_channel.id)
        end
      end

      def amazon_catalog_search
        sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id])
        brand_name = params[:brand_name]
        brands_array = brand_name.split(/[\s,]+/)
        formatted_brand_name = brands_array.join(',')
        keyword = params[:keyword].strip
        token = params[:token]&.sub(/=+$/, "")
        response = Amazon::CatalogApis::SearchCatalogItems.new(current_store.id, sale_channel.oauth_application.id).search(keyword, token, formatted_brand_name)
        broadcasting = [Apartment::Tenant.current, spree_current_user]
        if response.nil? || response.dig("numberOfResults") == 0
          QrCodeChannel.broadcast_to(broadcasting, type: 'show_error_message', response: 'Product not found')
        else
          QrCodeChannel.broadcast_to(broadcasting, type: 'amazon_catalog', response: response, sale_channel_id: sale_channel.id, keyword: params[:keyword], brand_name: formatted_brand_name)
        end
      end

      def get_catalog_items
        sale_channel = @listing.sale_channel
        asin = params['asin']
        response = Amazon::CatalogApis::GetCatalogItem.new(current_store.id, sale_channel.oauth_application.id).get_attribute(asin)
        product_type = response['productTypes'].first["productType"]
        category_name = product_type.split('_').map(&:capitalize).join(' ')
        category_id =  "#{product_type}_#{category_name}"
        render json: {payload: response, categoryName: category_name, categoryId: category_id, sale_channel_id: sale_channel.id, listingId: @listing.id}
      end

      def listing_issues
        sale_channel = @listing.sale_channel
        sku = @listing.sku
        response = ::Amazon::ListingApis::GetListingItem.new(current_store.id, sale_channel.oauth_application.id).get_listing_item(sku)
        error_message = []
        if response.dig('errors')
          error_message << response.dig('errors').first.dig('message')
        else
          response.dig('issues').each do |issue|
            next if issue.dig('severity') != "ERROR"
            error_message << issue.dig('message')
          end
        end
        error_message

        respond_to do |format|
          format.turbo_stream { render turbo_stream: turbo_stream.replace(
            "modal-content", partial: "spree/admin/listings/amazon/listing_error_message", locals: { error_messages: error_message }
          ) }
        end
      end

      def check_stock_availability_using_validator
        # expecting getting pack_size in params
        pack_size_toggle = params.dig(:pack_size, '0', :pack_size_toggle)
        pack_size = pack_size_toggle.eql?('true') ? params.dig(:pack_size, '0', :pack_size_value).to_i : 1

        listing = params[:listing_id].present? ? Spree::Listing.find(params['listing_id']) : nil

        availability_data = if params[:selected_variants].present?
                              validate_selected_variants(listing, pack_size, false)
                            else
                              validate_master_variant(listing, pack_size)
                            end

        respond_to do |format|
          format.json { render json: { data: availability_data } }
        end
      end

      def check_stock_availability_of_product
        product = params[:product_id].present? ? Spree::Product.find(params['product_id']) : nil
        listing = params[:listing_id].present? ? Spree::Listing.find(params['listing_id']) : nil

        availability_data = if params[:selected_variants].present?
                              validate_selected_variants(product, pack_size = 1, true)
                            else
                              validate_master_variant(nil)
                            end

        respond_to do |format|
          format.json { render json: { data: availability_data } }
        end
      end

      def params_for_validate_revise_walmart_listing
        product = @listing.product
        params_walmart = params.dig(:listing, :walmart) || params.dig(:walmart) || {}
        params_variants = []
        params_walmart.each do |variant_id, variant|
          params_variants << variant.merge({ variant_id: variant_id }) if variant_id.to_i > 0 && variant.present? && variant.is_a?(Hash)
        end

        first_params_variants = params_variants.first || {}

        additional = if product.variants.empty? || product.option_types.empty?
          {
            quantity: first_params_variants&.dig(:inventory_content, :shipNodes, 0, :quantity, :inputQty),
          }
        else
          variants = params_variants.map do |variant|
            {
              id: variant.dig(:variant_id),
              quantity: variant.dig(:inventory_content, :shipNodes, 0, :quantity, :inputQty),
            }
          end
        end

        params.merge(additional)
      end

      def revise_walmart_listing
        ActiveRecord::Base.transaction do
          begin
            update_listing_images(@listing, params)
            ::Listing::UpdateWalmartService.new(@listing, params, spree_current_user, request).revise_walmart_listing
            ::Listing::ValidateListingService.call(@listing, params_for_validate_revise_walmart_listing)
            if params[:commit] == "Save to draft"
              flash[:success] = Spree.t("admin.listing.save_to_draft")
              redirect_to admin_listings_path(listing_type: "draft")
            else
              flash[:success] = Spree.t("admin.listing.listing_successfully_updated")
              redirect_to admin_listings_path
            end
          rescue StandardError => e
            flash[:error] = Spree.t("admin.listing.something_went_wrong", message: e&.message)
            redirect_to admin_listings_path
            raise ActiveRecord::Rollback
          end
        end
      end

      def walmart_end_item
        result = ::Listing::UpdateWalmartService.new(@listing, params, spree_current_user, request).end_walmart_listing
        if result.success?
           record_activity_log
           end_item_redirect_to_success
        else
          end_item_redirect_to_error(result.error_message)
        end
      end

      def fetch_walmart_package_type
        oauth_application_id = Spree::SaleChannel.find_by(id: params[:id]).oauth_application.id
        package_types = Walmart::ShippingApis::PackageType.new(current_store.id, oauth_application_id).call
        render json: { message: 'success', data: package_types }
      end

      private

      def validate_variant_selected_or_not
        if @products.variants.any?
          return false unless listing_inventory_params[:variants].present?

          variants = listing_inventory_params[:variants].to_h
          selected = variants.map{|key, value| value[:is_selected] == "true"}.include?(true)
          return false unless selected
        end
        true
      end

      def validate_selected_variant_quantity
        if @products.variants.any?
          variants = listing_inventory_params[:variants].to_h
          selected_variants_ids = variants.select{|key, value| value[:is_selected] == "true"}&.keys&.map(&:to_i)
          selected_out_of_stock_variants = @products.variants.select do |var|
            var.variant_total_available <= 0 && selected_variants_ids.include?(var.id)
          end
          descriptive_name = selected_out_of_stock_variants.map(&:descriptive_name)

          return { status: false, message: "Please add stocks for #{descriptive_name.join(', ')} before publishing."} if selected_out_of_stock_variants.any?
        else
          return { status: false, message: "Please add stocks for product before publishing."} if @products.master.variant_total_available <= 0
        end
        { status: true }
      end

      def validate_stock_items
        result = listing_with_stock_items(@listing, @products)
        variant_ids = result&.keys&.map(&:to_i)
        variants = Spree::Variant.where(id: variant_ids)
        errors = []
        variants&.each do |variant|
          next if variant.total_available >= 1
          errors << "Please add stock for #{variant.descriptive_name} first"
        end
        errors
      end

      def validate_quantity_and_lot_size_for_listing(pack_size)
        @listing.product.total_available < pack_size
      end

      def destroy_listing_inventories_on_product_change
        @listing.listing_inventories.destroy_all
      end

      def update_listing_pricing_and_pack_size(pack_size)
        variants = listing_inventory_params[:variants]
        @listing.update_column(:pack_size_value, pack_size)
        if variants.nil?
          debugger
          create_or_update_listing_inventory(@listing.product.master.id, params[:listing][:price], params[:listing][:compare_at_price], params[:listing][:compare_to_price], true, params[:listing][:custom_quantity], params[:listing][:total_quantity], params[:listing][:quantity_type])
        else
          variants.each do |key, variant|
            create_or_update_listing_inventory(key, variant[:price], variant[:compare_at_price], variant[:compare_to_price], variant[:is_selected], variant[:custom_quantity], variant[:total_quantity], variant[:quantity_type])
          end
        end
      end

      def volume_pricing_attributes
        params.require(:listing).permit(
          volume_prices_attributes: [
            :id, :variant_id, :discount_type, :range, :amount,
            :position, :role_id, :name, :volume_price_model_id, :_destroy
          ]
        )
      end

      def set_volume_pricing_model
        if params[:listing]
          if params[:listing][:variant_volume_price_model_id] # for only master variant
            model_id = params[:listing][:variant_volume_price_model_id]&.to_i
            variant_id = @listing&.product&.master&.id
            variant = ::Spree::Variant.find(variant_id)
            if model_id > 0
              model = ::Spree::VolumePriceModel.find(model_id)
              # delete previous volume_price_model_id
              ::Spree::VolumePrice.where(listing_id: @listing&.id, variant_id: variant_id).where.not(volume_price_model_id: nil)&.delete_all
              model&.volume_prices&.each do |vp|
                new_name = vp&.name #temporarily using old name
                record = ::Spree::VolumePrice.find_or_initialize_by(listing_id: @listing&.id ,variant_id: variant_id, volume_price_model_id: model_id, name: new_name)
                record.update(
                  name: new_name,
                  range: vp&.range,
                  amount: vp&.amount,
                  position: vp&.position,
                  discount_type: vp&.discount_type,
                  role_id: vp&.role_id
                )
                # record.save
              end
            else
              ::Spree::VolumePrice.where(listing_id: @listing&.id, variant_id: variant_id).where.not(volume_price_model_id: nil)&.delete_all
            end
          elsif params[:listing][:variants]# for multi variants
            params[:listing][:variants]&.each do |variant_id, variant_hash|
              model_id = variant_hash['volume_price_model_id']&.to_i
              if model_id > 0
                variant = ::Spree::Variant.find(variant_id)
                model = ::Spree::VolumePriceModel.find(model_id)
                # delete previous volume_price_model_id
                ::Spree::VolumePrice.where(listing_id: @listing&.id, variant_id: variant_id).where.not(volume_price_model_id: nil)&.delete_all
                model&.volume_prices&.each do |vp|
                  new_name = vp&.name #temporarily using old name
                  record = ::Spree::VolumePrice.find_or_initialize_by(listing_id: @listing&.id ,variant_id: variant_id, volume_price_model_id: model_id, name: new_name)
                  record.update(
                    name: new_name,
                    range: vp&.range,
                    amount: vp&.amount,
                    position: vp&.position,
                    discount_type: vp&.discount_type,
                    role_id: vp&.role_id
                  )
                  # record.save
                end
              else
                ::Spree::VolumePrice.where(listing_id: @listing&.id, variant_id: variant_id).where.not(volume_price_model_id: nil)&.delete_all
              end
            end
          end
        end
      end

      def create_or_update_listing_inventory(variant_id, price, regular_price, compare_to, is_selected, custom_quantity, total_quantity, quantity_type)
        debugger
        listing_inventory = @listing.listing_inventories.find_or_initialize_by(variant_id:)
        listing_inventory.price = price
        listing_inventory.compare_at_price = regular_price
        listing_inventory.compare_to_price = compare_to
        listing_inventory.is_selected = ActiveModel::Type::Boolean.new.cast(is_selected || false)
        listing_inventory.total_listed_qty = quantity_type == "custom" ? custom_quantity.to_i : total_quantity.to_i
        listing_inventory.save
      end

      def set_permissions
        @permissions = current_user_permissions.find_permission
      end

      def current_user_permissions
        @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
      end

      def check_permission
        @permission = spree_current_user&.spree_roles&.first&.permission&.view_product_cost_price
      end

      def record_activity_log
        return unless spree_current_user && @listing

        action_key, action_place = determine_action_and_action_place(@listing)

        ::Spree::ActivityLog.create!(
          loggable: @listing,
          user_id: spree_current_user.id,
          action: ::Spree::ActivityLogActions.get(:listing, action_key),
          role: spree_current_user.spree_roles.first&.name,
          date: Time.current,
          email: spree_current_user.email,
          action_place: action_place,
          action_name: @listing.title,
          product_id: @listing.product&.id
        )
      end

      def determine_action_and_action_place(listing)
        case action_name
        when "new", "create"
          [:add, edit_admin_listing_path(listing)]
        when "update", "revise_active_listing"
          [:edit, edit_admin_listing_path(listing)]
        when "destroy", "end_item"
          [:remove, admin_listings_path]
        else
          [:default, "N/A"]
        end
      end


      def build_option_type_position(product)
        option_type_position = {}
        product.option_types.each { |ot| option_type_position[ot.id.to_s] = ot.position.to_s}
        option_type_position
      end

      def validate_selected_variants(record, _pack_size, is_product = false)
        complete_availability_data = []

        params[:selected_variants].each do |variant_hash|
          variant, input_qty = extract_variant_and_quantity(variant_hash)
          if is_product && input_qty.to_i > variant.stock_items.sum(&:count_on_hand)
            complete_availability_data << build_product_availability_data(variant, input_qty)
          elsif !is_product
            if current_store.risky_listing
              if input_qty > variant.total_available
                complete_availability_data << build_variant_availability_data_for_risky_listing(record, variant,
                                                                                                _pack_size)
              end
            else
              unless valid_variant_for_listing?(input_qty, record, variant)
                complete_availability_data << build_variant_availability_data(record, variant, _pack_size)
              end
            end
          end
        end

        complete_availability_data
      end

      def valid_variant_for_listing?(input_qty, listing, variant, pack_size = 1)
        validation_method = listing.present? ? :validate_for_published_listing : :validate_for_draft_listing
        send(validation_method, input_qty, listing, variant, pack_size)
      end

      def validate_master_variant(listing, pack_size = 1)
        complete_availability_data = []

        variant = listing.present? ? listing.product.master : Spree::Product.find(params['product_id']).master
        input_qty = params['quantity'].to_i

        if variant.product.variants.any? && variant.product.option_types.empty?
          variant_id = params['variant_id']
          variant = Spree::Variant.find(variant_id)
        end

        if current_store.risky_listing
          if input_qty > variant.total_available
            complete_availability_data << build_variant_availability_data_for_risky_listing(listing, variant, pack_size)
          end
        else
          complete_availability_data << build_variant_availability_data(listing, variant, pack_size) unless valid_variant_for_listing?(input_qty, listing, variant, pack_size)
        end

        complete_availability_data
      end

      def validate_for_published_listing(input_qty, listing, variant, pack_size = nil)
        if current_store.risky_listing
          true if variant.total_available >= input_qty
        else
          Ebay::Validator::ListingInventory.validate_published_listing_qty?(input_qty, listing, variant, pack_size)
        end
      end

      def validate_for_draft_listing(input_qty, listing, variant, pack_size = nil)
        if current_store.risky_listing
          true if variant.total_available >= input_qty
        else
          Ebay::Validator::ListingInventory.validate_draft_listing_qty?(input_qty, listing, variant, pack_size)
        end
      end

      def extract_variant_and_quantity(variant_hash)
        variant = Spree::Variant.find(variant_hash[1]['id'])
        input_qty = variant_hash[1]['quantity'].to_i
        [variant, input_qty]
      end

      def build_product_availability_data(variant, _input_qty)
        {
          'variant_id' => variant.id,
          'quantity' => variant.stock_items.sum(&:count_on_hand)
        }
      end

      def build_variant_availability_data(listing, variant, pack_size)
        available_quantity = calculate_available_quantity(listing, variant, pack_size)
        {
          'variant_id' => variant.id,
          'quantity' => available_quantity
        }
      end

      def build_variant_availability_data_for_risky_listing(listing, variant, pack_size)
        available_quantity = calculate_available_quantity_for_risky_listing(listing, variant, pack_size)
        {
          'variant_id' => variant.id,
          'quantity' => available_quantity
        }
      end

      def calculate_available_quantity_for_risky_listing(_listing, variant, pack_size)
        (variant.total_available / pack_size).floor
      end

      def calculate_available_quantity(listing, variant, pack_size)
        if listing.present?
          listing_quantity = listing.listing_inventories.find_by(variant_id: variant.id)&.total_available_qty || 0
          (listing_quantity + find_total_available_qty_for_variant(variant, pack_size)) / pack_size
        else
          find_total_available_qty_for_variant(variant, pack_size) / pack_size
        end
      end

      def find_total_available_qty_for_variant(variant, _pack_size)
        return if variant.blank?

        listings = variant.product.listings.where(status: 'Active')
        sum_of_total_available_qty = 0

        if listings.present?
          listings.each do |listing|
            if listing.listing_inventories.present?
              sum_of_total_available_qty += (listing.listing_inventories.find_by(variant_id: variant.id)&.total_available_qty || 0)
            end
          end
        end
        (variant.total_available - sum_of_total_available_qty)
      end

      def validator_run_before_api_call_for_draft(listing, pack_size)
        product = listing.product

        # Check for product variants without option types
        if product.variants.any? && product.option_types.empty?
          validate_for_single_variant_draft_listing(listing, product.master, pack_size)
        # Check if product variants are present
        elsif product.variants.any?
          validate_for_multiple_variant_draft_listing(listing, pack_size)
        # Default case: use product master as variant
        else
          validate_for_single_variant_draft_listing(listing, product.master, pack_size)
        end
      end

      def validator_for_allowoffer_run_before_api_call(type, listing, params)
        return true unless listing.allow_offer.present?
        product = listing.product
        listing_params = params['listing']
        if type == 'revise'
          listing_params = params
        end

        # Check for product variants without option types
        if product.variants.any? && product.option_types.empty?
          listing_price = listing_params['price'].to_f || listing_params[:price].to_f
        # Check if product variants are present
        elsif product.variants.any?
          v_prices = []
          variant_keys = params.select { |key| key.start_with?('variant') }
          variant_keys.each_key do |variant_key|
            variant_index = variant_key.split('_').last
            price = params.dig(:listing, "price_#{variant_index}").to_f
            v_prices.push(price) if price > 0
          end
          listing_price = v_prices.min()
        # Default case: use product master as variant
        else
          listing_price = listing_params['price'].to_f || listing_params[:price].to_f
        end
        return validate_for_allowoffer_price(listing, listing_price)
      end

      def validate_for_single_variant_draft_listing(listing, variant = nil, pack_size)
        input_qty = params.dig(:listing, :quantity).to_i
        variant ||= Spree::Variant.find(params.dig(:listing, :variant_id))

        validate_for_draft_listing(input_qty, listing, variant, pack_size)
      end

      def validate_for_multiple_variant_draft_listing(listing, pack_size)
        params[:variants].each do |_, variant_hash|
          input_qty = variant_hash['quantity'].to_i
          variant = Spree::Variant.find(variant_hash['id'])

          return false unless validate_for_draft_listing(input_qty, listing, variant, pack_size)
        end
        true
      end

      def validator_run_before_api_call_for_revise(listing, pack_size = 1)
        product = listing.product

        # Check for product variants without option types
        if product.variants.any? && product.option_types.empty?
          validate_for_single_variant_published_listing(listing, product.master, pack_size)
        # Check if product variants are present
        elsif product.variants.any?
          validate_for_multiple_variant_published_listing(listing, pack_size)
        # Default case: use product master as variant
        else
          validate_for_single_variant_published_listing(listing, product.master, pack_size)
        end
      end

      def validate_for_single_variant_published_listing(listing, variant = nil, pack_size)
        input_qty = params['quantity'].to_i
        variant ||= Spree::Variant.find(params['variant_id'])

        validate_for_published_listing(input_qty, listing, variant, pack_size)
      end

      def validate_for_multiple_variant_published_listing(listing, pack_size)
        index = 0
        while params["variant_#{index}"].present?
          variant_id = params["variant_#{index}"]
          quantity = params["quantity_#{index}"].to_i

          variant = Spree::Variant.find(variant_id)

          return false unless validate_for_published_listing(quantity, listing, variant, pack_size)

          index += 1
        end
        true
      end

      def find_or_create_listing_inventories_data_on_publish(listing)
        if listing.product.variants.any? && listing.product.option_types.empty?
          find_or_create_single_inventory_from_params(listing, params['listing'])
        elsif listing.product.variants.any?
          params[:variants].each do |value|
            find_or_create_single_inventory_from_params(listing, value[1])
          end
        else
          params_data = params[:listing].merge({ variant_id: listing.product.master.id })
          find_or_create_single_inventory_from_params(listing, params_data)
        end
      end

      def find_or_create_single_inventory_from_params(listing, inventory_params)
        variant_id = inventory_params['variant_id'] || inventory_params[:id]
        set_quantity = inventory_params['quantity'].to_i || inventory_params[:quantity].to_i
        price = inventory_params['price'].to_f || inventory_params[:price].to_f
        record = listing.listing_inventories.find_or_initialize_by(variant_id: variant_id)
        record.update(
          total_listed_qty: set_quantity,
          total_available_qty: set_quantity,
          price: price
        )
      end

      def update_inventory_data_in_listing_on_update(listing, prev_prod_id)
        product = listing.product

        if modify_variant
          if product.variants.any? && product.option_types.empty?
            update_inventory_for_new_product(listing, prev_prod_id)
          elsif product.variants.any?
            listing.listing_inventories.destroy_all
            update_inventory_for_multiple_variants(listing)
          else
            update_inventory_for_new_product(listing, prev_prod_id)
          end
        elsif product.variants.any? && product.option_types.empty?
          update_inventory_for_single_variant(listing)
        elsif product.variants.any?
          update_inventory_for_multiple_variants(listing)
        else
          update_inventory_for_single_variant(listing)
        end
      end

      def update_inventory_for_new_product(listing, prev_prod_id)
        prev_product = Spree::Product.find(prev_prod_id)
        prev_variant_id = prev_product.master.id
        quantity = params['quantity'].to_i
        price ||= params[:price].to_f
        variant_id = listing.product.master.id

        listed_inventory = listing.listing_inventories.find_or_create_by(variant_id: prev_variant_id)
        listed_qty = listed_inventory.total_sold_qty.to_i + quantity
        listed_inventory.update(total_listed_qty: listed_qty, price: price, variant_id: variant_id)
      end


      def update_inventory_for_single_variant(listing, variant_id = nil, quantity = nil, price = nil)
        quantity = (quantity.presence || params['quantity'].to_i)
        price ||= params[:price].to_f
        listed_inventory = if variant_id.present?
                             listing.listing_inventories.find_or_create_by(variant_id: variant_id)
                           else
                             listing.listing_inventories.take
                           end
        update_listed_inventory(listed_inventory, quantity, price)
      end

      def update_inventory_for_multiple_variants(listing)
        index = 0
        while params["variant_#{index}"].present?
          variant_id = params["variant_#{index}"]
          quantity = params["quantity_#{index}"].to_i
          price = params.dig(:listing, "price_#{index}").to_f
          update_inventory_for_single_variant(listing, variant_id, quantity, price)
          destroy_listing_inventory_for_removed_variant(variant_id, listing) if params['listing']["delete_#{index}"].present?
          index += 1
        end
      end

      def destroy_listing_inventory_for_removed_variant(variant_id, listing)
        listing.listing_inventories.find_by(variant_id: variant_id)&.destroy
      end

      def update_listed_inventory(listed_inventory, quantity, price)
        qty_difference = quantity - listed_inventory.total_available_qty
        new_total_listed_qty = listed_inventory.total_listed_qty + qty_difference

        listed_inventory.update(total_listed_qty: new_total_listed_qty, price: price)
      end

      def update_low_availability_alert(listing)
        product = listing.product
        if (product.variants.any? && product.option_types.empty?) || product.variants.empty?
          update_master_variant_low_availability_alert(listing)
        else
          update_multi_variant_low_availability_alert(listing)
        end
      end

      def update_multi_variant_low_availability_alert(listing)
        listing.product.variants.count.times do |index|
          next unless params["variant_#{index}"].present?

          inventory = listing.listing_inventories.find_by(variant_id: params["variant_#{index}"])
          next unless inventory.present?

          toggle_value = params["low_availability_toggle_#{index}"].present? ? true : false
          value = params["low_availability_value_#{index}"].to_i

          inventory.update(
            low_availability_value: value,
            low_availability_toggle: toggle_value
          )
        end
      end

      def update_master_variant_low_availability_alert(listing)
        inventory = listing.listing_inventories.take
        return unless inventory.present?

        inventory.update(
          low_availability_value: params['low_availability_value_0'].to_i,
          low_availability_toggle: params['low_availability_toggle_0'].present? ? true : false
        )
      end

      def generate_variant_specific_listing_params
        variant_keys = params.select { |key| key.start_with?('variant') }
        variant_specific_params = {}
        variant_keys.each_key do |variant_key|
          variant_index = variant_key.split('_').last
          variant_specific_params[variant_key.to_s] = {
            id: params[variant_key.to_s],
            price: params.dig(:listing, "price_#{variant_index}"),
            quantity: params["quantity_#{variant_index}"],
            stock_item: params.dig(:listing, "stock_item_#{variant_index}"),
            variant_delete: params.dig(:listing, "delete_#{variant_index}"),
            variant_sku: params.dig(:listing, "SKU_#{variant_index}"),
            variant_price: params.dig(:listing, "price_#{variant_index}"),
            compare_at_price: params.dig(:listing, "regular_price_#{variant_index}"),
            compare_to_price: params.dig(:listing, "compare_to_price_#{variant_index}")
          }
        end
        params[:variants] = variant_specific_params
        params
      end

      def update_listing_with_stock_items_on_publish(listing, product)
        result = listing_with_stock_items(listing, product)
        listing.update(variant_stock_items_data: result)
      end

      def listing_with_stock_items(listing, product)
        variant_stock_items_hash = {}
        if product.variants.any? && product.option_types.empty?
          variant_stock_items_hash =
            {
              params.dig(:listing, :variant_id) => {
                'stock_item_id' => params.dig(:listing, :stock_item),
                'quantity' => params[:listing][:quantity]
              }
            }
        elsif product.variants.any?
          params.dig(:variants).each do |_key, value|
            variant_stock_items_hash[value[:id]] = {
              'stock_item_id' => value[:stock_item],
              'quantity' => value[:quantity]
            }
          end
        else
          variant_stock_items_hash =
            {
              product.master.id => {
                'stock_item_id' => params.dig(:listing, :stock_item),
                'quantity' => params[:listing][:quantity]
              }
            }
        end
        variant_stock_items_hash
      end

      def update_listing_with_stock_items_on_revise(listing, product)
        variant_stock_items_data = modify_variant ? {} : listing.variant_stock_items_data

        if product.variants.any? && product.option_types.any?
          params.dig(:variants).each do |_key, value|
            if variant_stock_items_data[value[:id]].present?
              variant_stock_items_data[value[:id]]['quantity'] = value[:quantity]
              variant_stock_items_data[value[:id]]['variant_delete'] = value[:variant_delete]
              variant_stock_items_data[value[:id]]['variant_sku'] = value[:variant_sku]
              variant_stock_items_data[value[:id]]['variant_price'] = value[:variant_price]
            else
              variant_stock_items_data[value[:id]] = {
                'quantity' => value[:quantity],
                'sold_quantity' => '0',
                'stock_item_id' => value[:stock_item],
                'variant_delete' => value[:variant_delete],
                'variant_sku' => value[:variant_sku],
                'variant_price' => value[:variant_price]
              }
            end
          end
        else
          variant_id = listing.variant_stock_items_data&.keys&.[](0)&.to_s
          if (modify_variant)
            variant_data_hash = listing.variant_stock_items_data
            variant_stock_items_data = {
              product.master.id => {
                'quantity' =>  params[:quantity].to_i + variant_data_hash.dig(variant_id.to_s, 'sold_quantity').to_i,
                'sold_quantity' => variant_data_hash.dig(variant_id.to_s, 'sold_quantity').to_i
              }
            }
          else
            variant_stock_items_data[variant_id]['quantity'] = params['quantity']
          end
        end

        listing.update!(variant_stock_items_data: variant_stock_items_data)
      end

      def modify_variant
        @prev_prod_id != params['product_id'].to_i
      end

      def listing_params
        if params[:allow_offer].nil?
          params[:allow_offer] = 'false'
        end
        taxon_name_value_arr = params[:listing][:taxon]&.split('_')
        params.require(:listing).permit(:title, :description, :sale_channel_id, :allow_offer, :minimum_offer_price, :autoaccept_offer_price, :shipping_category_id).tap do |wl|
          wl[:product_id] = @products.id
          wl[:category_id] = taxon_name_value_arr&.first
          wl[:category_name] = taxon_name_value_arr&.second
          wl[:currency] = @products.currency
          wl[:shipping_id] = params.dig(:listing, :fullfillment_id)
          wl[:payment_id] = params.dig(:listing, :payment_id)
          wl[:return_id] = params.dig(:listing, :return_id)
          wl[:condition_id] = params.dig(:listing, :condition)
          wl[:image_classifier] = update_image_classifier
          wl[:sku] = @products.master.sku
          wl[:allow_offer] = params[:allow_offer]
          wl[:minimum_offer_price] = params[:minimum_offer_price]
          wl[:autoaccept_offer_price] = params[:autoaccept_offer_price]
        end
      end

      def listing_inventory_params
        params.require(:listing).permit(variants: [:is_selected, :compare_at_price, :compare_to_price, :price, :custom_quantity, :total_quantity, :quantity_type])
      end

      def find_listing
        @listing = Spree::Listing.find_by(id: params[:id])
      end

      def load_shipping_categories
        @shipping_categories = Spree::ShippingCategory.order(:name)
      end

      def set_product
        @products = Spree::Product.find_by(id: params[:listing][:product_id])
      end

      def set_stock_location
        @stock_location = Spree::StockLocation.find_by(id: params[:listing][:stock_location])
      end

      def update_listing(listing, response, response_type)
        listing.update(item_id: response[response_type]['ItemID'], start_time: response[response_type]['StartTime'],
                       end_time: response[response_type]['EndTime'], status: 'Active')
      end

      def update_listing_for_end_item(listing, response, response_type)
        listing.update(end_time: response[response_type]['EndTime'], status: 'Ended')
      end

      def update_listing_for_revise_item(listing, _response, params, prev_prod_id)
        listing.update(title: params[:title], description: params[:description])
        update_listing_with_stock_items_on_revise(listing, listing.product)
        update_inventory_data_in_listing_on_update(listing, prev_prod_id)
        update_pack_size(listing)
        update_low_availability_alert(@listing)
        update_auction_pricing(@products) if params.dig(:listing, :pricing_format) == "Auction"
      end

      def end_item_redirect_to_error(message)
        flash[:error] = Spree.t('admin.listing.something_went_wrong', message: message)
        redirect_to(request.referer || admin_listings_path)
      end

      def end_item_redirect_to_success
        flash[:success] = Spree.t('admin.listing.listing_successfully_ended')
        redirect_to admin_listings_path(listing_type: 'Ended')
      end

      def build_flash_message_for_end_item(response, response_type, message, _listing)
        if response.dig(response_type, :Ack) == 'Failure'
          flash[:error] = Spree.t('admin.listing.something_went_wrong', message: message)
          redirect_to(request.referer || admin_listings_path)
        else
          update_listing_for_end_item(@listing, response, response_type)
          record_activity_log
          flash[:success] = Spree.t('admin.listing.listing_successfully_ended')
          redirect_to admin_listings_path(listing_type: 'Ended')
        end
      end

      def build_flash_message_for_revise_item(response, response_type, message, listing, params, prev_prod_id)
        raise StandardError, message if response.dig(response_type, :Ack) == 'Failure'

        update_listing_for_revise_item(listing, response, params, prev_prod_id)
        record_activity_log
        flash[:success] = ::Spree.t('admin.listing.listing_successfully_updated')

        redirect_to admin_listings_path
      end

      def build_flash_message_for_revise_amazon_item(response, listing)
        if response['status'] == 'INVALID'
          error_messages = response['issues'].map { |error| error['message'] }.join(', ')
          raise StandardError, error_messages
        end
        flash[:success] = 'Listing successfully Accepted'
        listing.update!(status: 'Accepted')
        seller_id = listing.sale_channel.oauth_application.seller_name
        RedisStore.set(listing.sku + "-#{seller_id}", Apartment::Tenant.current)
        redirect_to admin_listings_path
      end

      def generate_custom_item_specific(item_specifics)
        item_specific_hash = (params[:item_specifics].presence || {})
        custom_item_specifics = item_specifics.gsub(/\s+/, '')&.split(';')
        return unless custom_item_specifics

        custom_item_specific_hash = {}
        custom_item_specifics.each do |cis|
          key_value_pair = cis.split(':')
          custom_item_specific_hash["Custom_#{key_value_pair[0]}"] = key_value_pair[1]
        end
        item_specific_hash.merge!(custom_item_specific_hash)
      end

      def create_listing_images(listing, params)
        if params.dig(:listing, :image_urls).present?
          params[:listing][:image_urls].each do |image_url|
            filename = File.basename(image_url[1])

            next if listing.image_files.attachments.map { |x| x.blob.filename_for_database }.include?(filename)

            listing.image_files.attach(io: URI.open(image_url[1]), filename: filename)
          end
        end

        add_listing_images(listing, params[:listing][:image_files]) if params.dig(:listing, :image_files).present?

        remove_listing_images(listing, params[:selected_remove_images]) if params[:selected_remove_images].present?
        params[:listing][:image_urls] = generate_image_urls(listing)
        params
      end

      def update_listing_images(listing, params)
        params[:image_files] ||= params.dig(:listing, :image_files)
        add_listing_images(listing, params[:image_files]) if params[:image_files].present?

        remove_listing_images(listing, params[:selected_remove_images]) if params[:selected_remove_images].present?
        params[:image_urls] = generate_image_urls(listing)
        params
      end

      def generate_image_urls(listing)
        image_urls = {}
        listing.image_files.attachments.each_with_index do |image, index|
          next if params[:selected_remove_images].present? && params[:selected_remove_images].include?(image.id.to_s)

          key = "#{image.filename.to_s.split('_').dig(1)}"
          key = key.present? ? key : listing.product&.master&.id&.to_s
          short_url = ::Shortener::ShortenedUrl.generate(main_app.rails_blob_url(image))
          shortner_url = "#{request.base_url}/old_admin/#{short_url.unique_key}"
          if image_urls.key?(key)
            # If the key exists, append the new value to the existing array
            image_urls[key] << shortner_url
          else
            # If the key doesn't exist, create a new array with the value
            image_urls[key] = [shortner_url]
          end
        end
        image_urls
      end

      def add_listing_images(listing, image_file_hash)
        files = []

        image_file_hash.each do |variant, image_files|
          @option_text = Spree::Variant.find_by(id: variant)&.options_text
          product_id = params['product_id'] || params['listing']['product_id']
          variants_presence = @listing.product.id != product_id.to_i ? Spree::Product.find_by(id: product_id.to_i)&.variants : listing.product.variants

          unless variants_presence&.any?
            @option_text = 'master'
            variant = listing.product.master.id
            image_files = image_file_hash if image_files.blank?
          end

          image_files.each do |image|
            Rails.logger.error "@@@@@@@@@@@@@@@@@@@Image: #{image}"
            begin
              if image.is_a?(String)
                blob = nil

                # Attempt to extract signed blob ID from the URL
                signed_id = image[/blobs\/proxy\/(.*?)\//, 1]
                signed_key = image.split('/').last(2).first unless signed_id

                if signed_id
                  blob = ActiveStorage::Blob.find_signed(signed_id)
                elsif signed_key
                  blob = ActiveStorage::Blob.find_signed(signed_key)
                  unless blob
                    payload = ActiveStorage.verifier.verified(signed_key, purpose: :blob_key)
                    if payload
                      blob = ActiveStorage::Blob.find_by(key: payload[:key])
                    else
                      Rails.logger.error "Could not verify signed blob key for URL: #{image}"
                      next
                    end
                  end
                else
                  Rails.logger.error "Could not extract signed ID or key from image URL: #{image}"
                  next
                end

                unless blob
                  Rails.logger.error "No ActiveStorage::Blob found for URL: #{image}"
                  next
                end

                Rails.logger.debug "@@@@@ Image blob: #{blob}"

                img = Tempfile.new([blob.filename.base, ".#{blob.filename.extension}"])
                img.binmode
                img.write(blob.download)
                img.rewind

                imagefilename = "#{blob.filename.base}.#{blob.filename.extension}"
                Rails.logger.debug "@@@@@ Image filename: #{imagefilename}"
              else
                img = image
                imagefilename = image.original_filename
              end

              @option_text = @option_text.presence || 'master'
              filename = "#{@option_text}_#{variant}_#{imagefilename}"

              next if listing.image_files.attachments.map { |x| x.blob.filename_for_database }.include?(filename)

              files << { io: img, filename: filename }
            rescue => e
              Rails.logger.error "Image processing error: #{e.class} - #{e.message} for image: #{image}"
              next
            end
          end

          break unless listing.product.variants.any?
        end

        if files.present?
          listing.image_files.attach(files)

          listing.image_files.last(files.size).each do |attachment|
            Spree::ImageUpload.find_or_create_by!(
              imageable: listing,
              image_id: attachment.id
            ) do |upload|
              upload.user = spree_current_user
            end
          end
        end
      end


      def remove_listing_images(listing, image_files)
        attachment_to_delete = listing.image_files.attachments.select { |record| image_files.include?(record.id.to_s) }
        attachment_to_delete.each(&:purge)
      end

      def update_pack_size(listing)
        listing.update({
                         pack_size_value: params[:pack_size_toggle].present? ? params[:pack_size] : 1,
                         pack_size_toggle: params[:pack_size_toggle].present?
                       })
      end

      def update_auction_pricing(product)
        auction_pricing_hash = @listing.auction_pricing
        auction_pricing_hash = {
          product.master.id => {
            'quantity' =>  params.dig(:listing, :quantity),
            'auction_duration' => params.dig(:listing, :auction_duration),
            'pricing_format' => params.dig(:listing, :pricing_format),
            'starting_bid' => params.dig(:listing, :starting_bid),
            'buy_it_now' => params.dig(:listing, :buy_it_now),
            'reserve_price' => params.dig(:listing, :reserve_price)
          }
        }

        @listing.update!(auction_pricing: auction_pricing_hash)
      end

      def validate_for_allowoffer_price(listing, listing_price)
        return false if listing.minimum_offer_price.present? && !listing.autoaccept_offer_price.present? && listing_price.present? && listing.minimum_offer_price.to_f >= listing_price
        return false if !listing.minimum_offer_price.present? && listing.autoaccept_offer_price.present? && listing_price.present? && listing.autoaccept_offer_price.to_f >= listing_price
        return false if listing.minimum_offer_price.present? && listing.autoaccept_offer_price.present? && listing_price.present? && (listing.autoaccept_offer_price.to_f >= listing_price || listing.minimum_offer_price.to_f > listing.autoaccept_offer_price.to_f)

        return true
      end

      private

      def subscription_details_params
        subscription_details = params.require(:listing).permit(subscription_details: [:discount_type, :recurring_discount, :subscription_toggle, :first_price_per_item, :recurring_price_per_item, :first_time_discount, :recurring_discount_checkbox]).to_h[:subscription_details]
      end

      def walmart_shipping_nodes
        Walmart::SettingApis::GetShippingShipnodes.new(current_store.id, @listing.sale_channel.oauth_application_id).from_cache
      end

      def get_walmart_images
        is_first_load = false
        walmart_images = {}
        product = @listing.product
        variants = product.variants_including_master
        variants.each do |variant|
          walmart_images[variant.id] = []
        end
        if @listing.draft? && @listing.image_files.empty? && @listing.updated_at == @listing.created_at
          is_first_load = true
          @listing.product.variant_images.each_with_index do |image, index|
            variant_id =  image.viewable_id
            next unless walmart_images.include?(variant_id)
            short_url = ::Shortener::ShortenedUrl.generate(main_app.rails_blob_url(image.attachment))
            short_url_str = "#{request.base_url}/admin/#{short_url.unique_key}"
            url = main_app.rails_blob_url(image.attachment)
            walmart_images[variant_id].push({index: index, url: url, short_url: short_url_str, id: image.id})
          end
        else
          @listing.image_files.each_with_index do |image, index|
            variant_id = image.filename.to_s.split('_')[1].to_i
            next unless walmart_images.include?(variant_id)
            short_url = ::Shortener::ShortenedUrl.generate(main_app.rails_blob_url(image))
            short_url_str = "#{request.base_url}/admin/#{short_url.unique_key}"
            url = main_app.rails_blob_url(image)
            walmart_images[variant_id].push({index: index, url: url, short_url: short_url_str, id: image.id})
          end
        end
        {is_first_load: is_first_load, images: walmart_images}
      end
    end
  end
end
