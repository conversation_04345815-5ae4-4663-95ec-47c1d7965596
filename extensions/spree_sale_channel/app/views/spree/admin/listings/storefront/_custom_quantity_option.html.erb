 <div class="row mb-4" data-controller="custom-quantity-listing">
  <div class="col">
    <label class="form-label fw-semibold fs-5 mb-2">
      <%= Spree.t(:choose_quantity_type) %>
    </label>

    <% variant_qty = find_total_available_quantity_from_listing(variant, listing) %>
    <% variant_qty = variant.total_available if variant_qty.zero? %>
    <% listing_inventory = listing.listing_inventories.find_by(variant_id: variant.id) %>
    <% is_custom_quantity = listing_inventory&.total_listed_qty.present? && listing_inventory.total_listed_qty != variant.total_available %>

    <div class="d-flex flex-wrap align-items-center gap-4">
      <div class="form-check form-check-inline d-flex align-items-center gap-2 fs-6 mb-0">
        <%= radio_button_tag "listing[variants][#{variant.id}][quantity_type]",
                            "total",
                            !is_custom_quantity,
                            id: "quantity_type_total_#{variant.id}",
                            class: "form-check-input",
                            data: { action: "custom-quantity-listing#toggle" } %>
        <%= label_tag "quantity_type_total_#{variant.id}", Spree.t(:total_quantity), class: "form-check-label" %>
      </div>

      <div class="form-check form-check-inline d-flex align-items-center gap-2 fs-6 mb-0">
        <%= radio_button_tag "listing[variants][#{variant.id}][quantity_type]",
                            "custom",
                            is_custom_quantity,
                            id: "quantity_type_custom_#{variant.id}",
                            class: "form-check-input",
                            data: { action: "custom-quantity-listing#toggle" } %>
        <%= label_tag "quantity_type_custom_#{variant.id}", Spree.t(:custom_quantity), class: "form-check-label" %>

        <div data-custom-quantity-listing-target="customQuantityField"
            class="ms-2"
            style="<%= is_custom_quantity ? 'display: block;' : 'display: none;' %>">
          <%= number_field_tag "listing[variants][#{variant.id}][custom_quantity]",
                              listing_inventory&.total_listed_qty,
                              id: "custom_quantity_input_#{variant.id}",
                              class: "form-control form-control-sm",
                              placeholder: "Qty",
                              min: 0,
                              max: variant.total_available,
                              step: 1,
                              style: "width: 100px;" %>
        </div>
      </div>
    </div>
    <%= hidden_field_tag "listing[variants][#{variant.id}][total_quantity]", variant.total_available, id: "final_quantity_#{variant.id}" %>
  </div>
</div>
