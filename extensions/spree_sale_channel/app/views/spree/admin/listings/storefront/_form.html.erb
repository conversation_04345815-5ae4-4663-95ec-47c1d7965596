<%= render partial: 'spree/admin/shared/listing_tabs' %>

<div class="row draft-listing-form" data-controller="listing" data-target-product-qty="<%= @products.total_available %>" data-target-product-status="<%= @products.status %>">
  <div class="col-12">
    <%= form_with model: [:admin, @listing], id: "storefront_publish_form" do |form| %>
      <%= form.hidden_field 'product_id', value: @products&.id %>
      <%= form.hidden_field 'sale_channel_id', value: @listing&.sale_channel_id %>
      <div class="tab-content" id="nav-tabContent">
        <div class="tab-pane fade show active" id="list-title" role="tabpanel" aria-labelledby="list-title-list">
          <div class="row">
            <div class="col-6">
              <div class="d-flex align-items-center">
                <div class="form-group w-100">
                  <%= form.label :product, Spree.t(:product) %>
                  <% if @listing.status == "Active" %>
                    <%= form.select :product, options_for_select(Spree::Product.pluck(:name, :id), @products&.id), { include_blank: true }, class: 'form-control select2', id: 'product-select' %>
                  <% else %>
                    <%= form.text_field :product, value: @products&.name, class: 'form-control', disabled: true %>
                  <% end %>
                </div>
                <%= link_to_with_icon 'b-edit.svg', '', edit_admin_product_url(@products.id), class: 'with-tip icon-link icon-edit btn ml-2', id:'product-id' unless @listing.status == 'Ended' %>
              </div>
            </div>
            <div class="col-6">
              <div>
                <%= form.label :title, class: 'font-weight-bold' %><br>
                <%= form.text_field :title, value:@title || @listing.title, id: 'listing-title', class:'form-control' %>
              </div>
            </div>
          </div>
          <div class="row mt-4">
            <div class="col">
              <div class="form-group custom-height">
                <%= form.label :'description', Spree.t('admin.listing.description') %>
                <%= form.text_area :description, value: @description || @listing&.description || @products&.description, id: 'product-description', class: "form-control spree-rte" %>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col">
              <div class="form-group d-flex justify-content-end">
                <a class="btn btn-black is-small" onclick="nextTabButton(this);" href="javascript:void(0);" data-target="list-image-list">next</a>
              </div>
            </div>
          </div>
        </div>

        <div class="tab-pane fade" id="list-image" role="tabpanel" aria-labelledby="list-image-list">
          <div>
            <div>
              <div>
                <% if @products.variants.any? %>
                  <h2>Listing images</h2>
                  <div class="m-3">
                    <div class="table-responsive">
                      <table class="table table-dragger table-images" data-hook="images_table" data-sortable-link="<%= update_positions_admin_listings_url(listing_id: @listing) %>">
                        <tbody id="sortVert">
                          <% get_option_types(@listing, @products).each do |option_type| %>
                            <tr id="<%= spree_dom_id option_type %>" data-hook="option_row">
                              <td class="move-handle text-center">
                                <%= svg_icon name: "sort.svg", width: '18', height: '18' %>
                              </td>
                              <td>
                                <div class="font-weight-bold">
                                  <%= option_type.name %>
                                </div>
                                <% option_type.option_values.each do |ov| %>
                                  <span class="badge badge-inactive"><%= ov.name %></span>
                                <% end %>
                                </td>
                            </tr>
                          <% end %>
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div class="col">
                    <div class="choose_file_main d-block">
                      <% @products.variants_including_master.each do |variant| %>
                        <div class="d-flex justify-content-between">
                          <%= form.text_field :variant, value: get_variant_name(variant), class: 'form-control w-50', disabled: true %>
                          <%= form.fields_for :image_files do |f| %>
                          <% end %>
                        </div>
                        <div class="row mt-3" id="product-image-preview-<%= variant.id %>">
                          <% @products.variant_images.each_with_index do |image, index| %>
                            <% next if variant.id !=  image.viewable_id %>
                            <div class="col-6 col-sm-4 col-md-3 col-lg-2 admin-listing-image-container" id="image-container-<%= image.attachment.filename.to_s %>">
                              <%= image_tag(main_app.rails_blob_url(image.attachment), width: '60', height: '125', class: 'border rounded card-img-top') %>
                            </div>
                          <% end %>
                        </div>
                      <% end %>
                    </div>
                  </div>
                <% else %>
                  <h2>Listing images</h2>
                  <div class="row" id="product-image-preview-<%= @listing.product.master.id %>">
                    <% @products.images.each_with_index do |image, index| %>
                      <div class="col-6 col-sm-4 col-md-3 col-lg-2 my-3 admin-listing-image-container" id="image-container-<%= image.attachment.filename.to_s %>">
                        <%= image_tag(main_app.rails_blob_url(image.attachment), width: '60', height: '125', class: 'border rounded card-img-top') %>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="form-group d-flex justify-content-end">
                <a class="btn btn-black is-small" onclick="nextTabButton(this);" href="javascript:void(0);" data-target="list-price-list">next</a>
              </div>
            </div>
          </div>
        </div>

        <div class="tab-pane fade" id="list-price" role="tabpanel" aria-labelledby="list-price-list">
          <% if @variants&.any? %>
            <% @variants.includes(:stock_locations).each_with_index do |variant, index| %>
              <% selected_variant = @listing.listing_inventories.find_by(variant_id: variant.id)&.is_selected %>
              <% toggle = find_subscription_field_value(@listing, variant.id, "subscription_toggle") %>
              <% selected_discount_type = find_subscription_field_value(@listing, variant.id, "discount_type") %>
              <% selected_volume_price_model_id = find_volume_price_model_id(@listing, variant) %>
              <% @listing.volume_prices.build(variant_id: variant.id) if @listing.volume_prices.where(variant_id: variant.id).empty?  %>
              <div class="row d-flex justify-content-between align-items-center" style="margin-right: 0vh;">
                <div class="col">
                  <h2><%= Spree.t(:variant) %> - <%= variant.descriptive_name %></h2>
                </div>
                <div>
                  <%= get_recommended_button(@listing, variant) %>
                </div>
              </div>

              <%=  render "storefront/custom_quantity_option", variant: variant, listing: @listing %>

              <div class="row">
                <div class="col">
                  <div class="">
                    <%= check_box_tag "listing[variants][#{variant.id}][is_selected]", true, selected_variant, { class: "product_ids", id: "selected_variants_"+"#{index}", disabled: false } %>
                  </div>
                </div>

                <div class="col">
                  <div class="">
                    <%= Spree.t(:sku) %>
                    <div>
                      <p class="font-weight-light mt-3"><%= variant&.sku %></p>
                    </div>
                  </div>
                </div>

                <div class="col">
                  <div class="form-group">
                    <% variant_regular_price = params['updated_product_id'].present? || @listing.listing_inventories.blank? ? variant&.compare_at_price&.to_f : fetch_listing_regular_price(@listing, variant.id) %>
                    <%= form.label "variants[#{variant.id}][compare_at_price]", Spree.t(:regular_price) %>
                    <div class="input-group mb-3">
                      <div class="input-group-prepend">
                        <span class="input-group-text" id="basic-addon1"><%= currency_symbol(current_currency) %></span>
                      </div>
                      <%= form.number_field "variants[#{variant.id}][compare_at_price]", value: variant_regular_price, class: 'form-control px-2', min: 0, step: 0.01 %>
                    </div>
                  </div>
                </div>

                <div class="col">
                  <div class="form-group">
                    <% variant_compare_to_price = params['updated_product_id'].present? || @listing.listing_inventories.blank? ? variant&.compare_to_price&.to_f : fetch_listing_compare_to_price(@listing, variant.id) %>
                    <%= form.label "variants[#{ variant.id }][compare_to_price]", Spree.t(:compare_to) %>
                    <div class="input-group mb-3">
                      <div class="input-group-prepend">
                        <span class="input-group-text" id="basic-addon1"><%= currency_symbol(current_currency) %></span>
                      </div>
                      <%= form.number_field "variants[#{ variant.id }][compare_to_price]", value: variant_compare_to_price, class: 'form-control px-2', min: 0, step: 0.01 %>
                    </div>
                  </div>
                </div>

                <div class="col">
                  <div class="form-group">
                    <% variant_price = params['updated_product_id'].present? || @listing.listing_inventories.blank? ? variant.price.to_f : fetch_listing_price(@listing, variant.id) %>
                    <%= form.label "variants[#{variant.id}][price]", Spree.t(:price) %>
                    <div class="input-group mb-3">
                      <div class="input-group-prepend">
                        <span class="input-group-text" id="basic-addon1"><%= currency_symbol(current_currency) %></span>
                      </div>
                      <%= form.number_field "variants[#{variant.id}][price]", value: variant_price, class: 'form-control px-2', id: "listing_price_#{index}", data: { action: 'input->listing#recalculate', variant_id: variant.id, discount_type: selected_discount_type, index: index }, min: 0, step: 0.01, required: true  %>
                    </div>
                  </div>
                </div>
              </div>


              <div class="row">
                <div class="col">
                  <div class="form-group">
                    <h4><%= Spree.t(:volume_price_model) %></h4>
                    <div class="input-group mb-3">
                      <%= form.collection_select("variants[#{variant.id}][volume_price_model_id]", Spree::VolumePriceModel.all, :id, :name, { include_blank: Spree.t('match_choices.none'), selected: selected_volume_price_model_id }, { class: 'select2', disabled: (cannot? :edit, Spree::VolumePriceModel) }) %>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row d-flex justify-content-between align-items-center mt-3" style="margin-right: 0vh;">
                <div class="col">
                  <h4><%= Spree.t(:volume_prices) %></h4>
                </div>
                <div>
                  <%= button_link_to Spree.t(:add_volume_price), 'javascript:;', {icon: 'add', data: {target: "tbody#volume_prices_#{index}"}, class: 'btn-success listing_volume_prices_add_fields', id: 'add_volume_price'} %>
                </div>
              </div>

              <div class="row">
                <table class="table">
                  <thead>
                    <tr>
                      <th><%= Spree.t(:name) %></th>
                      <th><%= Spree.t(:discount_type) %></th>
                      <th><%= Spree.t(:range) %></th>
                      <th><%= Spree.t(:amount) %></th>
                      <th><%= Spree.t(:position) %></th>
                      <th>Roles</th>
                      <th class="actions"></th>
                    </tr>
                  </thead>

                  <tbody id="volume_prices_<%= index %>">
                    <%= form.fields_for :volume_prices do |f| %>
                      <% if f.object.variant_id == variant.id %>
                        <tr class="volume_price fields">
                          <%= f.hidden_field :variant_id, value: variant.id, class: 'variant_id' %>
                          <td style="vertical-align: baseline;">
                            <%= f.text_field :name, class: 'form-control' %>
                          </td>
                          <td style="vertical-align: baseline; width: 15%;">
                            <%= f.select :discount_type, [
                                ["#{Spree.t(:total_price)}", 'price'],
                                ["#{Spree.t(:percent_discount)}", 'percent'],
                                ["#{Spree.t(:price_discount)}", 'dollar']
                              ], { include_blank: Spree.t('match_choices.none') }, class: 'select2' %>
                          </td>
                          <td style="vertical-align: baseline;">
                            <%= f.text_field :range, class: 'form-control' %>
                          </td>
                          <td style="vertical-align: baseline;">
                            <%= f.text_field :amount, class: 'form-control' %>
                          </td>
                          <td style="vertical-align: baseline;">
                            <%= f.text_field :position, class: 'form-control' %>
                          </td>
                          <td style="vertical-align: baseline;" class="w-25">
                            <%= f.collection_select(:role_id, Spree::Role.all, :id, :name, { include_blank: Spree.t('match_choices.none') }, { class: 'select2' }) %>
                          </td>
                          <td class="actions" style="vertical-align: baseline;">
                            <%= link_to_icon_remove_fields f %>
                          </td>
                        </tr>
                      <% end %>
                    <% end %>
                  </tbody>
                </table>
              </div>

              <hr>

              <div class="border rounded">
                <div class="my-4 mx-3">
                  <div class="custom-control custom-switch float-right">
                    <%= check_box_tag "listing[subscription_details][#{variant.id}][subscription_toggle]", true, toggle, class: 'custom-control-input large', id: "subscription_toggle_#{ index }", data: { action: 'change->listing#hideandshowsubscription', index: index, variant_id: variant.id } %>
                    <%= label_tag "subscription_toggle_#{ index }", '', style: 'transform: scale(1.4);', class: 'custom-control-label' %>
                  </div>
                    <strong class="h5 bold">Subscribe & Save</strong>
                    <p class="h6 mt-3">Allow buyer to schedule recurring orders of this product.</p>
                </div>
                <% display = toggle.present? ? 'd-block' : 'd-none' %>
                <div id="subscription_toggle_<%= index %>_div" class="<%= display %>">
                  <div class="col-lg-4 d-block mx-4">
                    <div class="form-group">
                      <strong class="ml-2">Discount Type</strong>
                      <ul>
                        <% options = ['Percent off discount', 'Dollar off discount'] %>
                        <% options.each do |option| %>
                          <li class="font-weight-normal">
                            <%= radio_button_tag "listing[subscription_details][#{variant.id}][discount_type]", option, option == find_subscription_field_value(@listing, variant.id, "discount_type"), id: option.downcase.gsub(' ', '_'), class:'mt-2 discount_type', style:'transform: scale(1.4);', data: { action: 'change->listing#setInputBoxIcon', target: variant.id, index: index } %>
                            <span class="form-label font-weight-light ml-1">
                              <%= option.capitalize %>
                            </span>
                          </li>
                        <% end %>
                      </ul>
                    </div>
                  </div>

                  <hr class="w-50 ml-3">

                  <div class="row ml-3 bold">
                    <div class="clo-2 px-3 ml-3">
                      <% @first_time_discount = find_subscription_field_value(@listing, variant.id, "first_time_discount").to_i %>
                      <% @first_time_discount_type = find_subscription_field_value(@listing, variant.id, "discount_type") %>
                      <% checkbox_toggle_value =  @first_time_discount_type == "Dollar off discount" ? true : false %>
                      <% svg_icon = checkbox_toggle_value.present? ? 'currency.svg' : 'percentage.svg' %>
                      <%= form.field_container :first_time_discount do %>
                        <%= form.label "subscription_details[#{ variant.id }][first_time_discount]", Spree.t('admin.listing.first_time_discount'), class:'text-lowercase' %>
                        <div class="input-group currency">
                          <div class="input-group-prepend">
                            <span class="input-group-text input-box-svg-icon">
                              <%= turbo_frame_tag "svg-container-#{ variant.id }" do %>
                                <%= render partial: '/spree/admin/listings/storefront/input_box_svg_icon', locals: { name: svg_icon } %>
                              <% end %>
                            </span>
                          </div>
                          <%= form.number_field "subscription_details[#{ variant.id }][first_time_discount]", min: checkbox_toggle_value ? 1 : 5, value: @first_time_discount, id:"first_#{ variant.id }_discount", class: 'form-control no-border-on-focus', required: true, style: 'width: 100px; height: auto;', data: { action: 'blur->listing#calculate', variant_price: variant_price, variant_id: variant.id, index: index } %>
                        </div>
                        <%= form.error_message_on "subscription_details[#{ variant.id }][first_time_discount]" %>
                      <% end %>
                    </div>
                    <div class="col-2 px-3">
                      <%= form.field_container :first_price_per_item do %>
                        <span class="form-label bold">
                          <%= Spree.t('admin.listing.price_per_item') %>
                        </span>
                        <div class="input-group currency mt-1 pt-2 px-3" style="transform: scale(1.3);">
                          <div class="input-group-prepend">
                            <span class="input-group-text border-0">
                              <%= svg_icon(name: 'currency.svg', width: 14, height: 16) %>
                            </span>
                          </div>
                          <% first_price_per_item = checkbox_toggle_value.present? ? (variant_price - @first_time_discount) : (variant_price - ((variant_price * @first_time_discount) / 100)) %>
                          <%= hidden_field_tag "listing[subscription_details][#{ variant.id }][first_price_per_item]", first_price_per_item, id: "first_#{variant.id}_price_per_item_field" %>
                          <div class="mt-1 font-weight-light" id="first_<%= variant.id %>_price_per_item">
                            <%= first_price_per_item %>
                          </div>
                        </div>
                        <%= form.error_message_on "listing[subscription_details][#{ variant.id }][first_price_per_item]" %>
                      <% end %>
                    </div>
                  </div>

                  <div class="row ml-3 pl-1">
                    <%= check_box_tag "listing[subscription_details][#{ variant.id }][recurring_discount_checkbox]", true, find_subscription_field_value(@listing, variant.id, "recurring_discount_checkbox"), class: "mr-3 recurring_discount_checkbox_#{variant.id}", style:'transform: scale(1.3);' %>
                    <div class="clo-2 pr-3">
                      <%= form.field_container :recurring_discount do %>
                      <% @recurring_discount = find_subscription_field_value(@listing, variant.id, "recurring_discount") %>
                        <%= form.label "subscription_details[#{ variant.id }][recurring_discount_checkbox]", Spree.t('admin.listing.recurring_discount'), class: 'text-lowercase' %>
                        <div class="input-group currency">
                          <div class="input-group-prepend">
                            <span class="input-group-text input-box-svg-icon span-recurring-discount-<%= variant.id %>">
                              <%= turbo_frame_tag "svg-container-currency-#{ variant.id }" do %>
                                <%= render partial: '/spree/admin/listings/storefront/input_box_svg_icon', locals: { name: svg_icon } %>
                              <% end %>
                            </span>
                          </div>
                          <%= form.number_field "subscription_details[#{ variant.id }][recurring_discount]", value: @recurring_discount.to_i, id: "recurring_#{ variant.id }_discount", class: 'form-control recurring_discount no-border-on-focus', required: true, style: 'width: 100px; height: auto;', data: { action: 'blur->listing#calculate', variant_price: variant_price, variant_id: variant.id, discount_type: @first_time_discount_type, recurring_discount: @recurring_discount, first_time_discount: @first_time_discount, index: index } %>
                        </div>
                        <%= form.error_message_on "subscription_details[#{ variant.id }][recurring_discount]" %>
                      <% end %>
                    </div>
                    <div class="clo-2 px-3">
                      <%= form.field_container :recurring_price_per_item do %>
                        <span class="form-label bold">
                          <%= Spree.t('admin.listing.price_per_item') %>
                        </span>
                        <div class="input-group currency mt-1 pt-2" style="transform: scale(1.3);">
                          <div class="input-group-prepend">
                            <span class="input-group-text border-0">
                              <%= svg_icon(name: 'currency.svg', width: 14, height: 16) %>
                            </span>
                          </div>
                          <% recurring_price_per_item = checkbox_toggle_value.present? ? (variant_price - @recurring_discount.to_i) : (variant_price - ((variant_price * @recurring_discount.to_i) / 100)) %>
                          <%= hidden_field_tag "listing[subscription_details][#{ variant.id }][recurring_price_per_item]", recurring_price_per_item, id: "recurring_#{variant.id}_price_per_item_field" %>
                          <div class="mt-1 font-weight-light" id="recurring_<%= variant.id %>_price_per_item">
                            <%= recurring_price_per_item %>
                          </div>
                        </div>
                        <%= form.error_message_on "listing[subscription_details][#{ variant.id }][recurring_price_per_item]" %>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>

              <hr>
            <% end %>
          <% elsif @variant&.present? %>
            <% selected_variant = @listing.listing_inventories&.find_by(variant_id: @variant&.id)&.is_selected %>
            <%= form.hidden_field :variant_id, value: @variant.id %>
            <div class="row ml-3">
              <div class="col">
                <div class="">
                  <%= Spree.t(:sku) %>
                  <div>
                    <b><%= @variant&.sku == ""? 'N/A' : @variant&.sku %></b>
                  </div>
                </div>
              </div>

              <div class="col">
                <div class="form-group">
                  <% variant_price = params['updated_product_id'].present? || @listing.listing_inventories.blank? ? @variant.price.to_f : fetch_listing_price(@listing, @variant.id) %>
                  <%= form.label :price, Spree.t(:price) %>
                  <div class="input-group mb-3">
                    <div class="input-group-prepend">
                      <span class="input-group-text" id="basic-addon1"><%= currency_symbol(current_currency) %></span>
                    </div>
                    <%= form.text_field :price, value: variant_price, class: 'form-control px-2' %>
                  </div>
                  <%= get_recommended_button(@listing, @variant) %>
                </div>
              </div>
            </div>
          <% else %>
            <% variant = @products&.master %>
            <% master_variant = variant.id %>
            <% variant_regular_price = params['updated_product_id'].present? || @listing.listing_inventories.blank? ? variant&.compare_at_price&.to_f : fetch_listing_regular_price(@listing, variant.id) %>
            <% variant_compare_to_price = params['updated_product_id'].present? || @listing.listing_inventories.blank? ? variant&.compare_to_price&.to_f : fetch_listing_compare_to_price(@listing, variant.id) %>
            <% variant_price = params['updated_product_id'].present? || @listing.listing_inventories.blank? ? variant.price.to_f : fetch_listing_price(@listing, variant.id) %>
            <% selected_volume_price_model_id = find_volume_price_model_id(@listing, variant) %>
            <% @listing.volume_prices.build(variant_id: variant.id) if @listing.volume_prices.where(variant_id: variant.id).empty?  %>

            <div class="row d-flex justify-content-between align-items-center" style="margin-right: 0vh;">
              <div class="col">
                <h2><%= Spree.t(:variant) %> - <%= variant.descriptive_name %></h2>
              </div>
              <div>
                <%= get_recommended_button(@listing, variant) %>
              </div>
            </div>

            <%=  render "storefront/custom_quantity_option", variant: variant, listing: @listing %>

            <div class="row">
              <div class="col">
                <div class="">
                  <%= Spree.t(:sku) %>
                  <div>
                    <b><%= variant&.sku == ""? 'N/A' : variant&.sku %></b>
                  </div>
                </div>
              </div>

              <div class="col">
                <div class="form-group">
                  <%= form.label :regular_price, Spree.t(:regular_price) %>
                  <div class="input-group mb-3">
                    <div class="input-group-prepend">
                      <span class="input-group-text" id="basic-addon1"><%= currency_symbol(current_currency) %></span>
                    </div>
                    <%= form.number_field :compare_at_price, value: variant_regular_price, class: 'form-control px-2', min: 0, step: 0.01 %>
                  </div>
                </div>
              </div>

              <div class="col">
                <div class="form-group">
                  <%= form.label :compare_to, Spree.t(:compare_to) %>
                  <div class="input-group mb-3">
                    <div class="input-group-prepend">
                      <span class="input-group-text" id="basic-addon1"><%= currency_symbol(current_currency) %></span>
                    </div>
                    <%= form.number_field :compare_to_price, value: variant_compare_to_price, class: 'form-control px-2', min: 0, step: 0.01 %>
                  </div>
                </div>
              </div>

              <div class="col">
                <div class="form-group">
                  <%= form.label :price, Spree.t(:price) %>
                  <% index = 1 %>
                  <div class="input-group mb-3">
                    <div class="input-group-prepend">
                      <span class="input-group-text" id="basic-addon1"><%= currency_symbol(current_currency) %></span>
                    </div>
                    <%= form.number_field :price, value: variant_price, class: 'form-control px-2', id: 'listing_price_1', data: { action: 'input->listing#recalculate', variant_id: master_variant, index: index }, min: 0, step: 0.01, required: true %>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
                <div class="col">
                  <div class="form-group">
                    <h4><%= Spree.t(:volume_price_model) %></h4>
                    <div class="input-group mb-3">
                      <%= form.collection_select("variant_volume_price_model_id", Spree::VolumePriceModel.all, :id, :name, { include_blank: Spree.t('match_choices.none'), selected: selected_volume_price_model_id }, { class: 'select2', disabled: (cannot? :edit, Spree::VolumePriceModel) }) %>
                    </div>
                  </div>
                </div>
            </div>

            <div class="row d-flex justify-content-between align-items-center mt-3" style="margin-right: 0vh;">
              <div class="col">
                <h4><%= Spree.t(:volume_prices) %></h4>
              </div>
              <div>
                <%= button_link_to Spree.t(:add_volume_price), 'javascript:;', {icon: 'add', data: {target: "tbody#volume_prices"}, class: 'btn-success listing_volume_prices_add_fields', id: 'add_volume_price'} %>
              </div>
            </div>

            <div class="row">
              <table class="table">
                <thead>
                  <tr>
                    <th><%= Spree.t(:name) %></th>
                    <th><%= Spree.t(:discount_type) %></th>
                    <th><%= Spree.t(:range) %></th>
                    <th><%= Spree.t(:amount) %></th>
                    <th><%= Spree.t(:position) %></th>
                    <th>Roles</th>
                    <th class="actions"></th>
                  </tr>
                </thead>
                <tbody id="volume_prices">
                  <%= form.fields_for :volume_prices do |f| %>
                    <% if f.object.variant_id == variant.id %>
                      <tr class="volume_price fields">
                        <%= f.hidden_field :variant_id, value: variant.id, class: 'variant_id' %>
                        <td style="vertical-align: baseline;">
                          <%= f.text_field :name, class: 'form-control' %>
                        </td>
                        <td style="vertical-align: baseline; width: 15%;">
                          <%= f.select :discount_type, [
                              ["#{Spree.t(:total_price)}", 'price'],
                              ["#{Spree.t(:percent_discount)}", 'percent'],
                              ["#{Spree.t(:price_discount)}", 'dollar']
                            ], { include_blank: Spree.t('match_choices.none') }, class: 'select2' %>
                        </td>
                        <td style="vertical-align: baseline;">
                          <%= f.text_field :range, class: 'form-control' %>
                        </td>
                        <td style="vertical-align: baseline;">
                          <%= f.text_field :amount, class: 'form-control' %>
                        </td>
                        <td style="vertical-align: baseline;">
                          <%= f.text_field :position, class: 'form-control' %>
                        </td>
                        <td style="vertical-align: baseline;" class="w-25">
                          <%= f.collection_select(:role_id, Spree::Role.all, :id, :name, { include_blank: Spree.t('match_choices.none') }, { class: 'select2' }) %>
                        </td>
                        <td class="actions" style="vertical-align: baseline;">
                          <%= link_to_icon_remove_fields f %>
                        </td>
                      </tr>
                    <% end %>
                  <% end %>
                </tbody>


              </table>
            </div>

            <hr>

            <div class="border rounded">
              <div class="my-4 mx-3">
                <% toggle =  find_subscription_field_value(@listing, master_variant, "subscription_toggle") %>
                <div class="custom-control custom-switch float-right">
                  <%= check_box_tag "listing[subscription_details][#{ master_variant }][subscription_toggle]", true, toggle, class: 'custom-control-input large', id: "subscription_toggle_#{index}", data: { action: 'change->listing#hideandshowsubscription', index: index, variant_id: master_variant } %>
                  <%= label_tag "subscription_toggle_#{index}", '', style: 'transform: scale(1.4);', class: 'custom-control-label bg-success' %>

                </div>
                  <strong class="h5 bold">Subscribe & Save</strong>
                  <p class="h6 mt-3">Allow buyer to schedule recurring orders of this product.</p>
              </div>
              <% display = toggle.present? ? 'd-block' : 'd-none' %>
              <div id="subscription_toggle_<%= index %>_div" class="<%= display %>">
                <div class="col-lg-4 d-block mx-4">
                  <div class="form-group">
                    <strong class="ml-2">Discount Type</strong>
                    <ul>
                      <% options = ['Percent off discount', 'Dollar off discount'] %>
                      <% options.each do |option| %>
                        <li>
                          <%= radio_button_tag "listing[subscription_details][#{ master_variant }][discount_type]", option, option == find_subscription_field_value(@listing, master_variant, "discount_type"), id: option.downcase.gsub(' ', '_'), class:'mt-2 discount_type', style:'transform: scale(1.4);', data: { action: 'change->listing#setInputBoxIcon', target: master_variant, index: index } %>
                          <span class="form-label font-weight-light ml-1">
                            <%= option.capitalize %>
                          </span>
                        </li>
                      <% end %>
                    </ul>
                  </div>
                </div>
                <hr class="w-50 ml-3">
                <div class="row ml-3 bold">
                  <div class="clo-2 px-3 ml-3">
                    <% @first_time_discount = find_subscription_field_value(@listing, master_variant, "first_time_discount").to_i %>
                    <% @discount_type = find_subscription_field_value(@listing, master_variant, "discount_type") %>
                    <% checkbox_toggle_value =  @discount_type == "Dollar off discount" ? true : false %>
                    <% svg_icon = checkbox_toggle_value.present? ? 'currency.svg' : 'percentage.svg' %>
                    <%= form.field_container :first_time_discount do %>
                      <%= form.label "subscription_details[#{ master_variant }][first_time_discount]", Spree.t('admin.listing.first_time_discount'), class: 'text-lowercase' %>
                      <div class="input-group currency">
                        <div class="input-group-prepend">
                          <span class="input-group-text input-box-svg-icon">
                            <%= turbo_frame_tag "svg-container-#{ master_variant }" do %>
                              <%= render partial: '/spree/admin/listings/storefront/input_box_svg_icon', locals: { name: svg_icon } %>
                            <% end %>
                          </span>
                        </div>
                        <%= form.number_field "subscription_details[#{ master_variant }][first_time_discount]", min: checkbox_toggle_value ? 1 : 5, value: @first_time_discount, id:"first_#{master_variant}_discount", class: 'form-control no-border-on-focus', required: true, style: 'width: 100px;', data: { action: 'blur->listing#calculate', variant_price: variant_price, variant_id: master_variant, index: index } %>
                      </div>
                      <%= form.error_message_on "subscription_details[#{ master_variant }][first_time_discount]" %>
                    <% end %>
                  </div>
                  <div class="clo-4 px-3">
                    <%= form.field_container :first_price_per_item do %>
                        <span class="form-label">
                          <%= Spree.t('admin.listing.price_per_item') %>
                        </span>
                      <div class="input-group currency mt-1 pt-2" style="transform: scale(1.3);">
                        <div class="input-group-prepend">
                          <span class="input-group-text border-0">
                             <%= svg_icon(name: 'currency.svg', width: 14, height: 16) %>
                          </span>
                        </div>
                        <% first_price_per_item = checkbox_toggle_value.present? ? (variant_price - @first_time_discount) : (variant_price - ((variant_price * @first_time_discount) / 100)) %>
                        <%= hidden_field_tag "listing[subscription_details][#{ master_variant }][first_price_per_item]", first_price_per_item, id: "first_#{master_variant}_price_per_item_field" %>
                        <div class="mt-1 font-weight-light" id="first_<%= master_variant %>_price_per_item">
                          <%= first_price_per_item %>
                        </div>
                      </div>
                      <%= form.error_message_on "listing[subscription_details][#{ master_variant }][first_price_per_item]" %>
                    <% end %>
                  </div>
                </div>
                <div class="row ml-3 pl-1">
                  <% @recurring_discount_type = find_subscription_field_value(@listing, master_variant, "discount_type") %>
                  <% checkbox_toggle_value =  @recurring_discount_type == "Dollar off discount" ? true : false %>
                  <%= hidden_field_tag "listing[subscription_details][#{master_variant}][recurring_discount_checkbox]", false %>
                  <%= check_box_tag "listing[subscription_details][#{ master_variant }][recurring_discount_checkbox]", true, find_subscription_field_value(@listing, master_variant, "recurring_discount_checkbox"), class: "mr-3 recurring_discount_checkbox_#{master_variant}", style:'transform: scale(1.3);' %>
                  <div class="clo-2 pr-3">
                    <% @recurring_discount = find_subscription_field_value(@listing, master_variant, "recurring_discount").to_i %>
                    <%= form.field_container :recurring_discount do %>
                      <%= form.label "subscription_details[#{ master_variant }][recurring_discount]", Spree.t('admin.listing.recurring_discount'), class: 'text-lowercase' %>
                      <div class="input-group currency">
                        <div class="input-group-prepend">
                          <span class="input-group-text span-recurring-discount-<%= master_variant %>">
                            <%= turbo_frame_tag "svg-container-currency-#{ master_variant }" do %>
                              <%= render partial: '/spree/admin/listings/storefront/input_box_svg_icon', locals: { name: svg_icon } %>
                            <% end %>
                          </span>
                        </div>
                        <%= form.number_field "subscription_details[#{ master_variant }][recurring_discount]", value: @recurring_discount, id: "recurring_#{master_variant}_discount", class: 'form-control recurring_discount no-border-on-focus', required: true, style: 'width: 100px;', data: { action: 'blur->listing#calculate', variant_price: variant_price, variant_id: master_variant, discount_type: @recurring_discount_type, recurring_discount: @recurring_discount, first_time_discount: @first_time_discount, index: index } %>
                      </div>
                      <%= form.error_message_on "subscription_details[#{ master_variant }][recurring_discount]" %>
                    <% end %>
                  </div>
                  <div class="clo-2 px-3">
                    <%= form.field_container :price_per_item do %>
                      <span class="form-label bold">
                        <%= Spree.t('admin.listing.price_per_item') %>
                      </span>
                      <div class="input-group currency mt-1 pt-2" style="transform: scale(1.3);">
                        <div class="input-group-prepend">
                          <span class="input-group-text border-0">
                            <%= svg_icon(name: 'currency.svg', width: 14, height: 16) %>
                          </span>
                        </div>
                        <% recurring_price_per_item = checkbox_toggle_value.present? ? (variant_price - @recurring_discount.to_i) : (variant_price - ((variant_price * @recurring_discount.to_i) / 100)) %>
                        <%= hidden_field_tag "listing[subscription_details][#{ master_variant }][recurring_price_per_item]", recurring_price_per_item, id: "recurring_#{master_variant}_price_per_item_field" %>
                        <div class="mt-1 font-weight-light" id="recurring_<%= master_variant %>_price_per_item">
                          <%= recurring_price_per_item %>
                        </div>
                      </div>
                      <%= form.error_message_on "listing[subscription_details][#{ master_variant }][recurring_price_per_item]" %>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>

            <hr>
          <% end %>

          <div class="border rounded">
            <div class="my-4 mx-3">
              <%= form.field_container :shipping_category do %>
                <%= form.label :shipping_category_id, Spree.t(:shipping_category), style: 'font-size: 1.25rem;' %>
                <%= form.collection_select(:shipping_category_id, @shipping_categories.order(:name), :id, :name, { include_blank: false }, { class: 'select2' }) %>
              <% end %>
            </div>
          </div>

          <div class="border rounded mt-3">
            <% div_class = @listing&.pack_size_value > 1 ? 'd-block' : 'd-none' %>
            <% pack_size_toggle = @listing&.pack_size_value > 1 ? true : false %>
            <div class="my-4 mx-3">
              <div class="custom-control custom-switch float-right">
                <%= check_box_tag 'pack_size_toggle', false, pack_size_toggle, class: 'custom-control-input large', id: 'pack_size_toggle', data: { action: 'change->listing#hideAndShow' } %>
                <%= label_tag 'pack_size_toggle', '', style: 'transform: scale(1.4);', class: 'custom-control-label' %>
              </div>
              <strong class="h5 bold">Sell as lot</strong>
              <p class="h6 mt-2">Group similar or identical items that you want to sell together to a single buyer. </p>
            </div>

            <div class="col-lg-2 <%= div_class %>" id="pack_size_div">
              <div class="form-group">
                <strong>Quantity in lot </strong>
                <%= form.number_field :pack_size, name: "pack_size", value: @listing&.pack_size_value || 1, class: 'form-control', id: 'pack_size_value', min: 1, max: @listing.product.total_available, required: true %>
              </div>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col">
              <div class="d-flex justify-content-end">
                <% message = product_active_status(@products) ? "Please update the product status to active first" : "" %>
                <%= form.submit "Save to draft", class: 'btn btn-black is-small listing-submit-button' if @listing.status == "draft" %>
                <%= form.submit "Publish", id: "submit-button", class: 'btn btn-black is-small listing-submit-button', title: message, disabled: product_active_status(@listing.product) if @listing.status == "draft" %>
                <%= form.submit "Update", id: "submit-button", class: 'btn btn-black is-small listing-submit-button', title: message, disabled: product_active_status(@products) if @listing.status == "Active" %>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
<h2>Product Specific Listings</h2>
<%= render 'product_specific_listing' if @products.listings.Active.present? %>
<%= stylesheet_link_tag 'listings' %>
<%= javascript_include_tag 'listings.js' %>
